/*===============================================
  Summernote
================================================= */
// editor
.note-editor {
    border: none;
}
// toolbar
.note-editor .note-toolbar {
    background: #fafafa;
    border: none;
    border-bottom: 1px solid #e7e7e7;
    padding: 0 10px 9px;
    margin: 0;
}
// toolbar btn groups
.note-editor .note-toolbar > .btn-group {
    margin-top: 8px;
}
// toolbar buttons
.note-editor .btn-sm,
.note-editor .btn-group-sm > .btn {
    padding: 3px 8px 4px;
}
.note-editor .btn-default {
    background-color: #FFF;
    border-color: #DDD;
    border-radius: 1px;
}
// textarea
.note-editor .note-editable {
    overflow: auto;
}
// status bar
.note-editor .note-statusbar {
    background: none;
}
.note-editor .note-statusbar .note-resizebar {
    border: none;
}
//note modal
.note-dialog .modal-dialog {
    padding-top: 50px;
}
