/**
 * General site alert !
 */
var message = {

    alert: function (text) {
        swal(text);
    },
    wait: function () {
        swal({
            title: "Lütfen bekleyiniz.",
            allowOutsideClick: false,
            showConfirmButton: false,
            onOpen: function() {
                swal.showLoading();
            }
        });
    },
    success: function (text,callback) {
        if(typeof callback == "function"){
            swal({
                    title: "",
                    text: text,
                    type: "success",
                },
                function () {
                    return callback();
                });
        }else{
            swal("Başarılı", text, "success");
        }
    },
    danger: function (text) {
        swal("Hata !", text, "error");
    },
    confirm: function (text, callback) {
        swal({
                title: "",
                text: text,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Evet",
                cancelButtonText: "Hayır",
                closeOnConfirm: false
            },
            function (isConfirm) {
                return callback(isConfirm);
            });
    },
    prompt: function (text,callback) {
        swal({
                title: text,
                text: "",
                type: "input",
                showCancelButton: true,
                closeOnConfirm: false,
                animation: "slide-from-top",
            },
            function(inputValue){
                return callback(inputValue);
            });
    },
    reload : function (time) {
        if(!time){
            time = 500;
        }
        setTimeout(function () {
            window.location.reload();
        },time);
    },
    close : function () {
        swal.close();
    }
};


var excelExport = function(){
    $('#excel_export').val(1);
    $('#form1').submit();
}
$("form button[type = 'submit']").click(function () {
    $('#excel_export').val(0);
});


$(document).ready(function () {
    $(".select2").select2();
});

$(document).ajaxSend(function(e,xhr,ajaxSettings,thrownError) {
    setTimeout(function(){
        if (xhr.status >= 500) {
            alert('Bir hata oluştu! Lütfen tekrar deneyin.');
        }
    }, 500);

});
