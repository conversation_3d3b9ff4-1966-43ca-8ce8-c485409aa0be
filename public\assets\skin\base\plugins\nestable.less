/*===============================================
  Nestable
================================================= */
.nestable-lists {
    .clearfix
}
.dd-handle,
.dd-dragel,
.dd-content {}
/*nestable*/
.dd {
    max-width: 100%;
}

/* Item heading */
.dd-handle {
    display: block;
    height: auto;
    cursor: pointer;
    margin: 5px 0;
    padding: 7px 10px;
    color: #777;
    text-decoration: none;
    font-weight: 600;
    border: 1px solid #ddd;
    background: #f5f5f5;
    -webkit-border-radius: 2px;
    border-radius: 2px;
}

/* heading hover */
.dd-handle:hover {
    color: #333;
    background: #ededed;
}
.dd-handle:hover + .dd-content {
    border-color: lighten(@brand-warning, 15%);
}
.dd-item > button {
    margin: 7px 0;
}

/* item content */
.dd-content {
    margin-top: -5px;
    padding: 10px;
    border: 1px solid #ddd;
    border-top: 0;
    background: #fafafa;
}
.dd-list .dd-list {
    padding-top: 5px;
    padding-bottom: 5px;
}

/* heading/content - dragged */
.dd-dragel .dd-handle,
.dd-dragel .dd-content {} // empty dd dragging port 
.dd-empty {
    background: #f8f8f8;
}
// dd handle contextuals
.dd-item.dd-primary > button,
.dd-item.dd-info > button {
    color: #FFF;
}
.dd-item.dd-primary .dd-handle {
    color: #FFF;
    background-color: #4a89dc !important;
    border-color: #4a89dc !important;
}
.dd-item.dd-info .dd-handle {
    color: #FFF;
    background-color: #3bafda !important;
    border-color: #3bafda !important;
}
