<?php
/**
 * Created by PhpStorm.
 * User: ykoken
 * Date: 12/27/2017
 * Time: 1:47 PM
 */

namespace App\Helper;

use App\Libraries\Microsoft\Graph\Graph;
use App\Libraries\Microsoft\Graph\Model\DateTimeTimeZone;
use App\Libraries\Microsoft\Graph\Model\Event;
use GuzzleHttp\Client;

class MicrosoftGraphApiHelper
{
    /**
     * @param $old
     * @param $new
     * @return string
     */
    public static function getAccessToken()
    {
        $client = new Client();
        $response = $client->post("https://login.microsoftonline.com/" . env('MICROSOFT_TENANT_ID') . "/oauth2/v2.0/token", [
            'form_params' => [
                'grant_type' => 'client_credentials',
                'client_id' => env('MICROSOFT_CLIENT_ID'),
                'client_secret' => env('MICROSOFT_CLIENT_SECRET'),
                'scope' => 'https://graph.microsoft.com/.default',
            ],
        ]);

        if ($response->getStatusCode() !== 200) {
            return null;
        }

        $body = json_decode($response->getBody(), true);
        return $body['access_token'];
    }

    public static function addCalendar($eventDetails, $testUsers, $pyUsers)
    {
        $accessToken = self::getAccessToken();

        if (!$accessToken) {
            return response()->json(['error' => 'Microsoft Graph erişim tokenı alınamadı'], 500);
        }

        if (is_array($eventDetails)) {
            $eventDetails = (object) $eventDetails;
        }


        $graph = new Graph();
        $graph->setAccessToken($accessToken);

        $timeZone = 'Europe/Istanbul';
        $start_datetime = date('Y-m-d\TH:i:s', strtotime($eventDetails->start_datetime));
        $end_datetime = date('Y-m-d\TH:i:s', strtotime($eventDetails->end_datetime));

        $event = new Event();
        $event->setSubject($eventDetails->session_title);
        $event->setBody([
            "contentType" => "HTML",
            "content" => self::setCalendarBodyContent($eventDetails),
        ]);
        $event->setStart(new DateTimeTimeZone([
            "dateTime" => $start_datetime,
            "timeZone" => $timeZone
        ]));
        $event->setEnd(new DateTimeTimeZone([
            "dateTime" => $end_datetime,
            "timeZone" => $timeZone
        ]));
        $attendees = [];
        if (!empty($testUsers)) {
            foreach ($testUsers as $testUser) {
                $attendees[] = [
                    "emailAddress" => [
                        "address" => $testUser['email'],
                        "name" => $testUser['name']
                    ],
                    "type" => "required"
                ];
            }
        }
        if (!empty($pyUsers)) {
            foreach ($pyUsers as $pyUser) {
                $attendees[] = [
                    "emailAddress" => [
                        "address" => $pyUser['email'],
                        "name" => $pyUser['name']
                    ],
                    "type" => "required"
                ];
            }
        }
        $extraAttendees = [
            ["address" => "<EMAIL>", "name" => "Project Vistream"],
            ["address" => "<EMAIL>", "name" => "Vistream Team"],
            ["address" => "<EMAIL>", "name" => "QA Vidizayn"],
            ["address" => "<EMAIL>", "name" => "Vistream Calendar"],
        ];

        if (!empty($extraAttendees)) {
            foreach ($extraAttendees as $attendee) {
                $attendees[] = [
                    "emailAddress" => [
                        "address" => $attendee['address'],
                        "name" => $attendee['name']
                    ],
                    "type" => "required"
                ];
            }
        }
        $event->setAttendees($attendees);

        try {
            $response = $graph->createRequest("POST", "/users/" . env('MICROSOFT_CALENDAR_USER') . "/events")
                ->attachBody($event)
                ->setReturnType(Event::class)
                ->execute();

            $outlookEventId = $response->getId();

            return response()->json(['success' => 'Etkinlik başarıyla eklendi!', 'outlookEventId' => $outlookEventId]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Etkinlik oluşturulurken hata oluştu: ' . $e->getMessage()], 500);
        }
    }

    public static function updateCalendar($outlook_event_id, $data_new, $testUsers, $pyUsers)
    {
        // Başlangıç tarihi geçmişse, takvimde güncelleme yapmayacak
        $startDate = strtotime($data_new->start_datetime);
        if ($startDate < time()) {
            return response()->json(['error' => 'Başlangıç tarihi geçmiştiği için, takvim güncellenmedi.'], 400);
        }

        $accessToken = self::getAccessToken();

        if (!$accessToken) {
            return response()->json(['error' => 'Microsoft Graph erişim tokenı alınamadı'], 500);
        }

        if (is_array($data_new)) {
            $data_new = (object) $data_new;
        }

        $graph = new Graph();
        $graph->setAccessToken($accessToken);

        $timeZone = 'Europe/Istanbul';
        $start_datetime = date('Y-m-d\TH:i:s', strtotime($data_new->start_datetime));
        $end_datetime = date('Y-m-d\TH:i:s', strtotime($data_new->end_datetime));

        $event = new Event();
        $event->setSubject($data_new->session_title);
        $event->setBody([
            "contentType" => "HTML",
            "content" => self::setCalendarBodyContent($data_new),
        ]);
        $event->setStart(new DateTimeTimeZone([
            "dateTime" => $start_datetime,
            "timeZone" => $timeZone
        ]));
        $event->setEnd(new DateTimeTimeZone([
            "dateTime" => $end_datetime,
            "timeZone" => $timeZone
        ]));

        $attendees = [];
        if (!empty($testUsers)) {
            foreach ($testUsers as $testUser) {
                $attendees[] = [
                    "emailAddress" => [
                        "address" => $testUser['email'],
                        "name" => $testUser['name']
                    ],
                    "type" => "required"
                ];
            }
        }
        if (!empty($pyUsers)) {
            foreach ($pyUsers as $pyUser) {
                $attendees[] = [
                    "emailAddress" => [
                        "address" => $pyUser['email'],
                        "name" => $pyUser['name']
                    ],
                    "type" => "required"
                ];
            }
        }
        $extraAttendees = [
            ["address" => "<EMAIL>", "name" => "Project Vistream"],
            ["address" => "<EMAIL>", "name" => "Vistream Team"],
            ["address" => "<EMAIL>", "name" => "QA Vidizayn"],
            ["address" => "<EMAIL>", "name" => "Vistream Calendar"],
        ];

        if (!empty($extraAttendees)) {
            foreach ($extraAttendees as $attendee) {
                $attendees[] = [
                    "emailAddress" => [
                        "address" => $attendee['address'],
                        "name" => $attendee['name']
                    ],
                    "type" => "required"
                ];
            }
        }
        $event->setAttendees($attendees);

        try {
            $graph->createRequest("PATCH", "/users/" . env('MICROSOFT_CALENDAR_USER') . "/events/" . $outlook_event_id)
                ->attachBody($event)
                ->setReturnType(Event::class)
                ->execute();

            return response()->json(['success' => 'Etkinlik başarıyla güncellendi!']);
        } catch (\Exception $e) {
            try {
                // If Event not found, create a new one
                return self::addCalendar($data_new, $testUsers, $pyUsers);
            } catch (\Exception $e) {
                return response()->json(['error' => 'Etkinlik güncellenirken hata oluştu (update->add): ' . $e->getMessage()], 500);
            }

            return response()->json(['error' => 'Etkinlik güncellenirken hata oluştu: ' . $e->getMessage()], 500);
        }
    }

    public static function deleteCalendar($outlook_event_id)
    {
        $accessToken = self::getAccessToken();

        $graph = new Graph();
        $graph->setAccessToken($accessToken);

        try {
            $graph->createRequest("DELETE", "/users/" . env('MICROSOFT_CALENDAR_USER') . "/events/" . $outlook_event_id)
                ->execute();

            return response()->json(['success' => 'Etkinlik başarıyla silindi!']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Etkinlik silinirken hata oluştu: ' . $e->getMessage()], 500);
        }
    }

    public static function setCalendarBodyContent($eventDetails)
    {
        $htmlicerik = '
                <h2>Yayın Bilgileri</h2>
                <ul>
                    <li><strong>Firma:</strong> ' . $eventDetails->customer_name . '</li>
                    <li><strong>Etkinlik Adı:</strong> ' . $eventDetails->event_name . '</li>
                    <li><strong>Yayın Adı:</strong> ' . $eventDetails->session_title . '</li>
                    <li><strong>Başlangıç Tarihi:</strong> ' . date('d/m/Y H:i', strtotime($eventDetails->start_datetime)) . '</li>
                    <li><strong>Bitiş Tarihi:</strong> ' . date('d/m/Y H:i', strtotime($eventDetails->end_datetime)) . '</li>
                    <li><strong>Yayın Tipi:</strong> ' . $eventDetails->session_type . '</li>
                    <li><strong>Ekip:</strong> ' . $eventDetails->session_users . '</li>
                    <li><strong>Web Sitesi:</strong> ' . $eventDetails->url . '</li>
                    <li><strong>Web Sitesi Şifresi:</strong> ' . $eventDetails->url_password . '</li>
                    <li><strong>Lokasyon:</strong> ' . $eventDetails->session_location . '</li>
                    <li><strong>Yayın Yapılacak Yer:</strong> ' . $eventDetails->location_name . '</li>
                    <li><strong>Lokasyon Açıklama:</strong> ' . $eventDetails->location_description . '</li>
                    <li><strong>Yayın Dili:</strong> ' . $eventDetails->main_lang . '</li>
                    <li><strong>Tercüme Dilleri:</strong> ' . $eventDetails->session_langs . '</li>
                    <li><strong>Notlar:</strong> ' . $eventDetails->note . '</li>
                </ul>
            ';

        return $htmlicerik;
    }
}