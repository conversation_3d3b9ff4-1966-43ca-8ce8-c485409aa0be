
/*************broadcast - head*************/
.broadcast-body {
    background-color: #20242D;
}

.broadcast {
    background-color: #20242D;
}

.broadcast_head_date {
    font-size: 30px;
    color: #fff;
    font-weight: bold;
}

.broadcast_head {
    padding: 1.5rem .6rem;
    border-bottom: 1px solid #373C46;
}

.broadcast_head-pstn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
}
.broadcast_head-pstn #reportrange{
    font-size: 30px;
    color: #fff;
    font-weight: bold;
}

.broadcast_head-pstn-right {
    display: flex;
    align-items: center;
}

.broadcast_head_option-pstn {
    display: flex;
}

.broadcast_head_option {
    color: #fff;
    font-size: 14px;
}

.broadcast_head_color {
    width: 14px;
    height: 14px;
    ;
    display: inline-block;
    margin-right: .6rem;
    border-radius: 2px;
}

.bg-red {
    background-color: #FA695E;
}

.bg-yellow {
    background-color: #FCBC05;
}

.bg-blue {
    background-color: #4485F4;
}

.broadcast_head_option-mr {
    margin-right: 1.5rem;
    display: flex;
    align-items: center;
}
.broadcast_head_option-mr:hover, .broadcast_head_option-mr:active {
    text-decoration: none !important;
    outline: none !important;
}

.broadcast_head-logo {
    width: 100%;
    max-width: 150px;
    margin-left: 1.5rem;
}

.broadcast_head-logo-out {
    width: 100%;
    max-width: 20px;
}

.logout {
    border: 1px solid #373c46;
    padding: 1.2rem;
    border-radius: 5px;
    margin-left: 3rem;
    transition: all .5s;
    cursor: pointer;
}

.logout:hover{
    background-color: rgb(250, 105, 94);
}

/*************broadcast - head*************/


/*************broadcast - body*************/

.broadcst_body_pstn {
    display: flex;
    width: 100%;
    padding: 2rem 1.2rem;
}

.broadcst_body-col1 {
    min-width: 65px;
    max-width: 65px;
    display: flex;
    align-items: center;
}

.broadcst_body-col2 {
    min-width: 140px;
    max-width: 140px;
    display: flex;
    align-items: center;
}

.broadcst_body-col3 {
    min-width: 110px;
    max-width: 110px;
    display: flex;
    align-items: center;
}

.broadcst_body-col4 {
    width: 100%;
    display: flex;
    align-items: center;
}

.broadcst_body-col5 {
    min-width: 150px;
    max-width: 150px;
    display: flex;
    align-items: center;
}

.broadcst_body-col6 {
    min-width: 270px;
    max-width: 270px;
    display: flex;
    align-items: center;
}

.broadcst_body-col7 {
    min-width: 220px;
    max-width: 220px;
    display: flex;
    align-items: center;
}

.broadcst_body-icon {
    width: 100%;
    max-width: 20px;
    display: flex;
    align-items: center;
    margin-right: .6rem;
}

.broadcst_body-icon-user {
    width: 100%;
    max-width: 13px;
    margin-right: .6rem;
}

.broadcst_body-option {
    color: #4485F4;
    font-size: 14px;
}


.broadcst_body-row1 {
    min-width: 65px;
    max-width: 65px;
    display: flex;
    align-items: center;
}

.broadcst_body-row2 {
    min-width: 140px;
    max-width: 140px;
    display: flex;
    flex-direction: column;
}

.broadcst_body-row3 {
    min-width: 110px;
    max-width: 110px;
    display: flex;
    padding-right: 5px;
}

.broadcst_body-row4 {
    width: 100%;
    display: flex;
    padding-right: 5px;
}

.broadcst_body-row5 {
    min-width: 150px;
    max-width: 150px;
    display: flex;
}

.broadcst_body-row6 {
    min-width: 270px;
    max-width: 270px;
    display: flex;
    flex-wrap: wrap;
}

.broadcst_body-row7 {
    min-width: 220px;
    max-width: 220px;
    display: flex;
}

.live-broadcast-img {
    width: 100%;
    max-width: 50px;
}

.broadcast_head-pstn-left {
    display: flex;
    align-items: center;
}

.broadcast_head_arrow {
    border-color: #fff transparent transparent transparent;
    border-style: solid;
    border-width: 9px 6px 0px 6px;
    width: 0px;
    height: 0px;
    margin-left: .7rem;
}

.calendar-white-icon {
    background-color: #373c46;
    padding: 1rem;
    border-radius: 5px;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.broadcst_body_row-pstn {
    display: flex;
    width: 100%;
    background-color: #373c46;
    padding: 1.2rem;
    border-radius: 13px;
    color: #fff;
    font-size: 13px;
    margin-bottom: 1rem;
}

.broadcst_body-row-date {

    font-weight: bold;
}

.portrait-en {
    color: #20242D;
    background-color: #9B9EA3;
    font-size: 9px;
    padding: .5rem;
    border-radius: 50%;
    font-weight: bold;
}

.broadcst_portrait-position {
    width: 100%;
    max-width: 135px;
}


/*************broadcast - body*************/

/************ daterangepicker ***********/
.daterangepicker {
    background-color: #20242D !important;
}

.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
    color: #fff !important;
}

.daterangepicker .calendar-table table,
.daterangepicker .calendar-table {
    background-color: #20242D !important;
}

.daterangepicker .calendar-table {
    border: 1px solid #20242D !important;
}

.daterangepicker td.in-range {
    background-color: #313133 !important;
}

.daterangepicker td.off,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date,
.daterangepicker td.off.end-date {
    background-color: #20242D !important;
    color: rgb(201 200 204 / 53%) !important;
}

.daterangepicker td.end-date {
    background-color: #357ebd !important;
}

.daterangepicker .drp-selected {
    color: #fff !important;
}

.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
    border-color: #fff !important
}

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
    background-color: rgb(49, 49, 51) !important;
}

.daterangepicker .drp-buttons .btn{
    display: flex !important;
    transition: all .5s;
}

.daterangepicker .drp-buttons .btn:hover{
    transform: scale(1.04);
}

.daterangepicker .drp-buttons{
 padding: 11px !important;
}

.btn-default{
    margin-left: auto !important;
    color: #C9C8CC !important;
    background-color: #313133 !important;
    border-color: transparent !important;
}
.daterangepicker.show-calendar .drp-buttons{
    display: flex !important;
    align-items: center !important;
}
/************ daterangepicker ***********/
@media screen and (min-width:1400px) {

    .broadcst_body-row5,
    .broadcst_body-col5 {
        min-width: 180px;
        max-width: 180px;
    }

    .broadcst_body-row6,
    .broadcst_body-col6 {
        min-width: 325px;
        max-width: 325px;
    }
}

@media screen and (min-width:1500px) {

    .broadcst_body-row5,
    .broadcst_body-col5 {
        min-width: 180px;
        max-width: 180px;
    }

    .broadcst_body-row6,
    .broadcst_body-col6 {
        min-width: 350px;
        max-width: 350px;
    }

    .broadcst_portrait-position {
        max-width: 140px;
    }

    .broadcst_body_row-pstn {
        font-size: 14px;
    }

    .broadcst_body-row2,
    .broadcst_body-col2 {
        min-width: 160px;
        max-width: 160px;
    }
}


@media screen and (max-width:1250px) {
    .table-scroll {
        overflow-x: scroll;
    }

    .table-broadcst {
        width: 1250px;
    }

    .table-scroll::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        background-color: #F5F5F5;
    }

    .table-scroll::-webkit-scrollbar {
        width: 12px;
        background-color: #F5F5F5;
        height: 12px;
        border-radius: 12px;
    }

    .table-scroll::-webkit-scrollbar-thumb {
        border-radius: 10px;
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
        background-color: #dee2e6;
    }
}

@media screen and (max-width:1200px) {
    .broadcast_head-pstn {
        justify-content: center;
        flex-direction: column;
    }
}

@media screen and (min-width:1250px) and (max-width: 1400px) {
    .broadcst_body-col4{
        min-width: 300px;
        max-width: 300px;
    }
    .broadcst_body-row4 {
        min-width: 300px;
        max-width: 300px;
        display: inline;
        padding-right: 5px;
    }

    .broadcst_body-col6 {
        min-width: 220px;
        max-width: 220px;
    }
    .broadcst_body-row6 {
        min-width: 220px;
        max-width: 220px;
        display: inline;
    }

    .broadcst_body-col7 {
        min-width: 180px;
        max-width: 180px;
    }
    .broadcst_body-row7 {
        min-width: 180px;
        max-width: 180px;
        display: inline;
    }
}

#main:before {
    background: #20242D !important;
}

.long-url {
    font-size: 80%;
}