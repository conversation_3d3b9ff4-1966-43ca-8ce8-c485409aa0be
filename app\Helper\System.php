<?php

namespace App\Helper;

use App\Models\Events;
use App\Models\Log;
use App\Models\Sessions;
use Illuminate\Support\Facades\Auth;

class System
{

    public static function convertOrders($items)
    {

        foreach ($items as $key => $value) {
            if (is_array($items[$key])) {
                $items[$key] = $value;
            } else {
                unset($items[$key]);
            }
        }

        $keys = array_keys($items);

        $result = [];

        if (isset($keys[0])) {
            for ($i = 0; $i < count($items[$keys[0]]); $i++) {
                foreach ($keys as $key) {
                    $result[$i][$key] = $items[$key][$i];
                }
            }
        }
        return $result;
    }

    public static function convertDate($date, $default_format = "Y-m-d H:i:s")
    {
        if (!empty($date)) {
            return date($default_format, strtotime($date));
        }

        return NULL;
    }

    public static function replaceWithValue($text, $val, $search = ':val')
    {
        return str_replace($search, $val, $text);
    }

    public static function dbResultToArray($data)
    {
        return array_map(function (&$obj) {
            return (array)$obj;
        }, $data);
    }


    /*
        public static function strSlug($str){
            $find = array('Ç', 'Ş', 'Ğ', 'Ü', 'İ', 'Ö', 'ç', 'ş', 'ğ', 'ü', 'ö', 'ı');
            $replace = array('c', 's', 'g', 'u', 'i', 'o', 'c', 's', 'g', 'u', 'o', 'i');
            $str = strtolower(str_replace($find, $replace, $str));
            $str = preg_replace("@[^A-Za-z0-9\-_\.\+]@i", ' ', $str);
            $str = trim(preg_replace('/\s+/', ' ', $str));
            $str = str_replace(' ', '-', $str);
            return $str;
        }
    */

    /**
     * @param $str
     * @param array $options
     * @return string
     */
    public static function strSlug($str, $options = array())
    {
        $str = mb_convert_encoding((string)$str, 'UTF-8', mb_list_encodings());
        $defaults = array(
            'delimiter'     => '-',
            'limit'         => null,
            'lowercase'     => true,
            'replacements'  => array(),
            'transliterate' => true
        );
        $options = array_merge($defaults, $options);
        $char_map = array(
            // Latin
            'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A', 'Æ' => 'AE', 'Ç' => 'C',
            'È' => 'E', 'É' => 'E', 'Ê' => 'E', 'Ë' => 'E', 'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
            'Ð' => 'D', 'Ñ' => 'N', 'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O', 'Ő' => 'O',
            'Ø' => 'O', 'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U', 'Ű' => 'U', 'Ý' => 'Y', 'Þ' => 'TH',
            'ß' => 'ss',
            'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a', 'æ' => 'ae', 'ç' => 'c',
            'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e', 'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
            'ð' => 'd', 'ñ' => 'n', 'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o', 'ő' => 'o',
            'ø' => 'o', 'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u', 'ű' => 'u', 'ý' => 'y', 'þ' => 'th',
            'ÿ' => 'y',
            // Latin symbols
            '©' => '(c)',
            // Greek
            'Α' => 'A', 'Β' => 'B', 'Γ' => 'G', 'Δ' => 'D', 'Ε' => 'E', 'Ζ' => 'Z', 'Η' => 'H', 'Θ' => '8',
            'Ι' => 'I', 'Κ' => 'K', 'Λ' => 'L', 'Μ' => 'M', 'Ν' => 'N', 'Ξ' => '3', 'Ο' => 'O', 'Π' => 'P',
            'Ρ' => 'R', 'Σ' => 'S', 'Τ' => 'T', 'Υ' => 'Y', 'Φ' => 'F', 'Χ' => 'X', 'Ψ' => 'PS', 'Ω' => 'W',
            'Ά' => 'A', 'Έ' => 'E', 'Ί' => 'I', 'Ό' => 'O', 'Ύ' => 'Y', 'Ή' => 'H', 'Ώ' => 'W', 'Ϊ' => 'I',
            'Ϋ' => 'Y',
            'α' => 'a', 'β' => 'b', 'γ' => 'g', 'δ' => 'd', 'ε' => 'e', 'ζ' => 'z', 'η' => 'h', 'θ' => '8',
            'ι' => 'i', 'κ' => 'k', 'λ' => 'l', 'μ' => 'm', 'ν' => 'n', 'ξ' => '3', 'ο' => 'o', 'π' => 'p',
            'ρ' => 'r', 'σ' => 's', 'τ' => 't', 'υ' => 'y', 'φ' => 'f', 'χ' => 'x', 'ψ' => 'ps', 'ω' => 'w',
            'ά' => 'a', 'έ' => 'e', 'ί' => 'i', 'ό' => 'o', 'ύ' => 'y', 'ή' => 'h', 'ώ' => 'w', 'ς' => 's',
            'ϊ' => 'i', 'ΰ' => 'y', 'ϋ' => 'y', 'ΐ' => 'i',
            // Turkish
            'Ş' => 'S', 'İ' => 'I', 'Ç' => 'C', 'Ü' => 'U', 'Ö' => 'O', 'Ğ' => 'G',
            'ş' => 's', 'ı' => 'i', 'ç' => 'c', 'ü' => 'u', 'ö' => 'o', 'ğ' => 'g',
            // Russian
            'А' => 'A', 'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D', 'Е' => 'E', 'Ё' => 'Yo', 'Ж' => 'Zh',
            'З' => 'Z', 'И' => 'I', 'Й' => 'J', 'К' => 'K', 'Л' => 'L', 'М' => 'M', 'Н' => 'N', 'О' => 'O',
            'П' => 'P', 'Р' => 'R', 'С' => 'S', 'Т' => 'T', 'У' => 'U', 'Ф' => 'F', 'Х' => 'H', 'Ц' => 'C',
            'Ч' => 'Ch', 'Ш' => 'Sh', 'Щ' => 'Sh', 'Ъ' => '', 'Ы' => 'Y', 'Ь' => '', 'Э' => 'E', 'Ю' => 'Yu',
            'Я' => 'Ya',
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ё' => 'yo', 'ж' => 'zh',
            'з' => 'z', 'и' => 'i', 'й' => 'j', 'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n', 'о' => 'o',
            'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'c',
            'ч' => 'ch', 'ш' => 'sh', 'щ' => 'sh', 'ъ' => '', 'ы' => 'y', 'ь' => '', 'э' => 'e', 'ю' => 'yu',
            'я' => 'ya',
            // Ukrainian
            'Є' => 'Ye', 'І' => 'I', 'Ї' => 'Yi', 'Ґ' => 'G',
            'є' => 'ye', 'і' => 'i', 'ї' => 'yi', 'ґ' => 'g',
            // Czech
            'Č' => 'C', 'Ď' => 'D', 'Ě' => 'E', 'Ň' => 'N', 'Ř' => 'R', 'Š' => 'S', 'Ť' => 'T', 'Ů' => 'U',
            'Ž' => 'Z',
            'č' => 'c', 'ď' => 'd', 'ě' => 'e', 'ň' => 'n', 'ř' => 'r', 'š' => 's', 'ť' => 't', 'ů' => 'u',
            'ž' => 'z',
            // Polish
            'Ą' => 'A', 'Ć' => 'C', 'Ę' => 'e', 'Ł' => 'L', 'Ń' => 'N', 'Ó' => 'o', 'Ś' => 'S', 'Ź' => 'Z',
            'Ż' => 'Z',
            'ą' => 'a', 'ć' => 'c', 'ę' => 'e', 'ł' => 'l', 'ń' => 'n', 'ó' => 'o', 'ś' => 's', 'ź' => 'z',
            'ż' => 'z',
            // Latvian
            'Ā' => 'A', 'Č' => 'C', 'Ē' => 'E', 'Ģ' => 'G', 'Ī' => 'i', 'Ķ' => 'k', 'Ļ' => 'L', 'Ņ' => 'N',
            'Š' => 'S', 'Ū' => 'u', 'Ž' => 'Z',
            'ā' => 'a', 'č' => 'c', 'ē' => 'e', 'ģ' => 'g', 'ī' => 'i', 'ķ' => 'k', 'ļ' => 'l', 'ņ' => 'n',
            'š' => 's', 'ū' => 'u', 'ž' => 'z'
        );
        $str = preg_replace(array_keys($options['replacements']), $options['replacements'], $str);
        if ($options['transliterate']) {
            $str = str_replace(array_keys($char_map), $char_map, $str);
        }
        $str = preg_replace('/[^\p{L}\p{Nd}]+/u', $options['delimiter'], $str);
        $str = preg_replace('/(' . preg_quote($options['delimiter'], '/') . '){2,}/', '$1', $str);
        $str = mb_substr($str, 0, ($options['limit'] ? $options['limit'] : mb_strlen($str, 'UTF-8')), 'UTF-8');
        $str = trim($str, $options['delimiter']);
        return $options['lowercase'] ? mb_strtolower($str, 'UTF-8') : $str;
    }

    /**
     * @param $field
     * @param null $as
     * @param bool $time
     * @return \Illuminate\Database\Query\Expression
     */
    public static function MysqlQueryIsoToDate($field, $as = null, $time = false)
    {
        $format = '%d.%m.%Y';
        if ($time)
            $format .= ' %H:%i';

        $as = $as ?: $field;
        return \DB::raw("DATE_FORMAT($field,'$format') as `$as`");
    }

    public static function SetMysqlNumberFormat($field, $as = null, $number = 2)
    {
        $as = $as ?: $field;
        return \DB::raw("FORMAT($field, $number) as `$as`");
    }


    public static function betweenDiffValue($old = array(), $new = array())
    {
        if (array_diff($old, $new)) {
            return "<s>".$old[0]."</s>";
        }
        return "";
    }

    public static function regions(){
        return [
            'AFME' => 'AFME',
            'Europe' => 'Europe',
            'Global' => 'Global',
            'Asia' => 'Asia',
            'Türkiye' => 'Türkiye',
            'North America' => 'North America',
            'LATAM - Mexico' => 'LATAM - Mexico',
            'LATAM - Argentina' => 'LATAM - Argentina',
            'LATAM - Brazil' => 'LATAM - Brazil',
            'LATAM - Chile' => 'LATAM - Chile',
            'LATAM - CAC' => 'LATAM - CAC',
            'LATAM - Colombia' => 'LATAM - Colombia',
            'LATAM - Ecuador / Perú / Bolivia' => 'LATAM - Ecuador / Perú / Bolivia',
        ];
    }

    public static function customers(){
        return array(
            '7Tur' => '7Tur',
            'Abbvie' => 'Abbvie',
            'Ali Raif İlaçları' => 'Ali Raif İlaçları',
            'Allergan' => 'Allergan',
            'AMGEN' => 'AMGEN',
            'Astra Zeneca' => 'Astra Zeneca',
            'Atölye Grup' => 'Atölye Grup',
            'Bayer' => 'Bayer',
            'D Event' => 'D Event',
            'directComm' => 'directComm',
            'Encore Medical Education' => 'Encore Medical Education',
            'Ethicon' => 'Ethicon',
            'Forum Turizm' => 'Forum Turizm',
            'Gilead' => 'Gilead',
            'GSK' => 'GSK',
            'HC Trading' => 'HC Trading',
            'HealthCare21' => 'HealthCare21',
            'Innovaacom' => 'Innovaacom',
            'İstanbul Eczacı Odası' => 'İstanbul Eczacı Odası',
            'Janssen' => 'Janssen',
            'Johnson & Johnson' => 'Johnson & Johnson',
            'MIMS' => 'MIMS',
            'MSD' => 'MSD',
            'Mylan' => 'Mylan',
            'Niceye Group' => 'Niceye Group',
            'Novo Nordisk' => 'Novo Nordisk',
            'Ogilvy CommonHealth' => 'Ogilvy CommonHealth',
            'Opteamist' => 'Opteamist',
            'Pfizer' => 'Pfizer',
            'Pitstop' => 'Pitstop',
            'RB' => 'RB',
            'Teva' => 'Teva',
            'The AAD Ltd' => 'The AAD Ltd',
            'TTP' => 'TTP',
            'Türkiye Romatoloji Derneği' => 'Türkiye Romatoloji Derneği',
            'Upjohn' => 'Upjohn',
            'Viatris' => 'Viatris',
            'VSY Biyoteknoloji ve İlaç Sanayi' => 'VSY Biyoteknoloji ve İlaç Sanayi',
            'Weber Shandwick' => 'Weber Shandwick',
            'Eisai' => 'Eisai',
            'SmileAgency' => 'SmileAgency',
            'custom_value' => 'Diğer'
        );
    }

    public static function getSessionCustomerName($session_id){
        $getSession = Sessions::query()->where('id', $session_id)->first();
        $eventDetail = Events::query()->where('id', $getSession->events_id)->first();

        return $eventDetail->customer_name;
    }

    public static function getEmAsiaCountryIds($type = "all"){
        if ($type == "em"){
            // Em Asia Countries
            $country_ids = ["94", "95", "125", "157", "164", "187", "203", "207", "227", "233"];
        }else if ($type == "au_nz"){
            // Australia(13) or New Zeland(149)
            $country_ids = ["13", "149"];
        }else {
            $country_ids = ["13", "94", "95", "125", "149", "157", "164", "187", "203", "207", "227", "233"];
        }
        return $country_ids;
    }

    public static function insertLog($oldData, $newData, $type, $location, $item_id, $itemName){
        $adminInfo = Auth::user();

        $data = [
            'admin_id' => $adminInfo->id,
            'email' => $adminInfo->email,
            'log_type' => $type,
            'log_section' => $location
        ];

        if ($newData == ""){
            // Delete
            $data['item_id'] = $item_id;
            $data['item_name'] = $itemName;
            $data['old_data'] = self::removeJsonProperty($oldData);
            $data['new_data'] = "";

            self::insertLogSave($data);
        } else if ($oldData == ""){
            // Add
            $data['item_id'] = $item_id;
            $data['item_name'] = $itemName;
            $data['old_data'] ="";
            $data['new_data'] = self::removeJsonProperty($newData);

            self::insertLogSave($data);
        } else {
            // Edit
            $changes = self::getChangedColumns($oldData, $newData);

            if ($itemName != ""){
                $itemName .= ' -> ';
            }

            if ($changes) {
                foreach ($changes as $key => $value) {
                    $data['item_id'] = $item_id;
                    $data['item_name'] = $itemName . $key;
                    $data['old_data'] = $oldData->$key;
                    $data['new_data'] = $value;

                    self::insertLogSave($data);
                }
            }
        }
    }
    public static function getChangedColumns($oldData, $newData){
        $object1 = json_decode(json_encode($newData), true);
        $object2 = json_decode(json_encode($oldData), true);

        if (isset($object1['password'])) { unset($object1['password']); }
        if (isset($object2['password'])) { unset($object2['password']); }

        return array_diff_assoc($object1, $object2);
    }
    public static function removeJsonProperty($data){
        $object = json_decode(json_encode($data, true), true);

        if (isset($object['password'])) { unset($object['password']); }

        return json_encode($object, true);
    }
    public static function insertLogSave($data){
        $log = new Log();
        $log->admin_id = $data['admin_id'];
        $log->email = $data['email'];
        $log->log_type = $data['log_type'];
        $log->log_section = $data['log_section'];
        $log->item_id = $data['item_id'];
        $log->item_name = $data['item_name'];
        $log->old_data = $data['old_data'];
        $log->new_data = $data['new_data'];
        $log->save();
    }

    public static function getRegionShortCode($regionName)
    {
        switch ($regionName){
            case "AFME": $region = 'AFM'; break;
            case "Europe": $region = 'EUR'; break;
            case "Global": $region = 'GLO'; break;
            case "Asia": $region = 'ASI'; break;
            case "Türkiye": $region = 'TUR'; break;
            case "North America": $region = 'NUS'; break;
            case "LATAM - Mexico": $region = 'LMX'; break;
            case "LATAM - Argentina": $region = 'LAR'; break;
            case "LATAM - Brazil": $region = 'LBR'; break;
            case "LATAM - Chile": $region = 'LCL'; break;
            case "LATAM - CAC": $region = 'LCC'; break;
            case "LATAM - Colombia": $region = 'LCO'; break;
            case "LATAM - Ecuador / Perú / Bolivia": $region = 'LEP'; break;
            default: $region = "DFT"; break;
        }

        return $region;
    }

    public static function generateNewStreamCode($session_id, $customer_name, $region, $start_date){
        return implode('.', [
            'VS',
            strtoupper(str_split($customer_name, 3)[0]),
            $region,
            date('dmy', strtotime($start_date)),
            $session_id
        ]);
    }

    public static function changeStreamCodeWithNewDate($session_id, $stream_id, $new_date){
        $streamIdParts = explode('.', $stream_id);
        $streamIdParts[3] = date('dmy', strtotime($new_date));

        $updatedStreamId = implode('.', $streamIdParts);

        Sessions::where('id', $session_id)->update([
            'stream_id' => $updatedStreamId
        ]);

        return $updatedStreamId;
    }

}
