var startDateTextBox = $('#datetimepicker1');
var endDateTextBox = $('#datetimepicker2');

$.timepicker.datetimeRange(
    startDateTextBox,
    endDateTextBox,
    {
        minInterval: (1000*60*30), // 1hr
        dateFormat: "dd/mm/yy",
        timeFormat: "HH:mm",
        defaultDate: "+1w",
        changeYear: true,
        changeMonth: true,
        numberOfMonths: 1,
        timeText: 'Zaman',
        hourText: 'Saat',
        minuteText: '<PERSON><PERSON>ka',
        start: {}, // start picker options
        end: {}, // end picker options,
        monthNamesShort: [ "Ocak", "Şubat", "Mart", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>rn", "Tmmz", "Ağts", "Eylül", "Ekim", "Kasım", "Aralık" ],
        dayNamesMin: [ "Pa", "Pt", "Sl", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ct" ],
        nextText: 'İleri',
        prevText: '<PERSON>eri',
        firstDay:1
    }

);
var startDateTextBox = $('#sessionstartdateeditdatepicker');
var endDateTextBox = $('#sessionenddateeditdatepicker');

$.timepicker.datetimeRange(
    startDateTextBox,
    endDateTextBox,
    {
        minInterval: (1000*60*30), // 1hr
        dateFormat: "dd/mm/yy",
        timeFormat: "HH:mm",
        defaultDate: "+1w",
        changeYear: true,
        changeMonth: true,
        numberOfMonths: 1,
        timeText: 'Zaman',
        hourText: 'Saat',
        minuteText: 'Dakika',
        start: {}, // start picker options
        end: {}, // end picker options
        monthNamesShort: [ "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Hzrn", "Tmmz", "Ağts", "Eylül", "Ekim", "Kasım", "Aralık" ],
        dayNamesMin: [ "Pa", "Pt", "Sl", "Ça", "Pe", "Cu", "Ct" ],
        nextText: 'İleri',
        prevText: 'Geri',
        firstDay:1
    }
);
// $.extend($.datepicker,{_checkOffset:function(inst,offset,isFixed){return offset}});

$('.orderrequestdate').datepicker({
    dateFormat: "dd/mm/yy",
    monthNames: [ "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" ],
    dayNamesMin: [ "Pa", "Pt", "Sl", "Ça", "Pe", "Cu", "Ct" ],
    nextText: 'İleri',
    prevText: 'Geri',
    firstDay:1
});
$('.ordersentdate').datepicker({
    dateFormat: "dd/mm/yy",
    monthNames: [ "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" ],
    dayNamesMin: [ "Pa", "Pt", "Sl", "Ça", "Pe", "Cu", "Ct" ],
    nextText: 'İleri',
    prevText: 'Geri',
    firstDay:1
});

//Orderdaki Fatura Ekleme Kısmındaki Fatura Tarihi
$(document).on("focus", ".invoice_date", function(){
    $(this).datepicker({ dateFormat: "dd/mm/yy",
        monthNames: [ "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" ],
        dayNamesMin: [ "Pa", "Pt", "Sl", "Ça", "Pe", "Cu", "Ct" ],
        nextText: 'İleri',
        prevText: 'Geri',
        firstDay:1});
});

$(document).on("keypress",".sayikontrol3", function(event){
    if(event.keyCode != 8 && event.keyCode != 0 && (event.keyCode < 48 || event.keyCode > 57))
        return false;
});


$('.inline-dtp').datetimepicker({
    prevText: '<i class="fa fa-chevron-left"></i>',
    nextText: '<i class="fa fa-chevron-right"></i>',
});

