/*

Dedicated Update Styles
Current Update: v1.2
Help: <EMAIL>

This file contains all of the style changes made in update v1.2
It exist to help a user track down a problem that arised after patching.
These changes already exist in theme.css and SHOULD NOT be added again
Please see the update changelog for more details.

 - LESS users should never modify the AdminDesigns base theme. Style customizations
and additions should always be placed to your child theme (skins/default-skin/less). 

- Standard CSS users should never modify theme.css. All style customizations and
additions should be added to a new stylesheet and included separately in your pages. 

Seperating your work and customizations from the themes default styles is 
absolutely key to smoothly transitioning through theme patches and updates.

It is also highly recommended that ALL users use the themes LESS system
as it is far superior, and offers a great deal of features and optimizations.
Please contact us, or leave a comment if you would like to see a LESS tutorial
added to the themes documentation.

*/


/*=======================================================
  Changes added in update v1.2
  - Navbar fixes and responsive changes
  - Sidebar responsive changes
  - Tray responsive styles
========================================================= */

/*Navbar Bug Fixes */
/* Modify whitespace for user menu dropdown - Firefox alignment bug fix */
.navbar .navbar-right > li:last-child > .dropdown-menu > li > a {
    white-space: inherit;
}

/*Navbar Responsive Changes */
@media (max-width: 1100px) {

    /*user menu dropdown modifications (hide name only display avatar)*/
    .navbar .navbar-right:last-child { margin-right: 5px; }
    .navbar .navbar-right > li:last-child > a > img { margin-right: 5px !important; }
    .navbar .navbar-right > li:last-child > a > span { display: none; }
    .navbar .navbar-right > li:last-child > a > span.caret { display: inline-block; }

     /*navbar Search Mobile Mode  */
     .navbar-form.navbar-search { 
        padding: 21px 6px 19px;
        margin: 0;
        cursor: pointer;
        width: auto;
        float: left;
     }
     .navbar-form.navbar-search .form-group { margin: 0 !important;}
     /*convert search bar to single icon*/
     .navbar-form.navbar-search:after { 
        content: "\f02e";
        position: relative;
        font: normal normal 16px octicons;
        line-height: 1;
        display: inline-block;
        text-decoration: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
     }
     /*transform searchbar input to menu slider*/
     .navbar-form.navbar-search input { 
        opacity: 0;
        z-index: -1;
        visibility: hidden;
        position: absolute;
        top: -60px;
        left: 0;
        width: 100%;
        height: 59px;
        margin: 0;
        font-size: 24px;
        color: #AAA;    
        border-radius: 0;
        border: 0;
        text-align: center;
        -webkit-transition: all 0.3s ease;
        transition: all 0.3s ease;
     }
     .navbar-form.navbar-search input:focus,
     .navbar.bg-light .navbar-form.navbar-search input:focus { 
        background-color: #FFF;
     }
     /*slide menu down when set to open(search icon clicked)*/
     .navbar-form.navbar-search.search-open input { 
        top: 0;
        opacity: 1;
        visibility: visible;
        z-index: 1040;
     }
     /*close search bar icon*/
     .navbar-form.navbar-search .search-remove { 
        opacity: 0;
        position: absolute;
        top: 0;
        right: 0;
        height: 59px;
        line-height: 59px;
        padding-left: 15px;
        padding-right: 25px;
        -webkit-transition: all 0.3s ease;
        transition: all 0.3s ease;
     }
     .navbar-form.navbar-search .search-remove:after { 
        position: relative;
        content: "\e014";
        font-family: 'Glyphicons Halflings';
        text-align: center;
        font-size: 20px;
     }
     .navbar-form.navbar-search.search-open .search-remove { 
        opacity: 1;
        z-index: 1040;
     }
     .navbar-form.navbar-search.search-open .search-remove:hover { 
        color: lighten(@brand-danger, 10%);
     }

}

/*Force Navbar Dropdowns to float and
to occupy 100% width at low resolutions */
@media (max-width: 480px) {

     /*set parent to static*/
     .sb-l-m .navbar > .navbar-right,
     .navbar .navbar-nav > li.dropdown {
        position: static !important;
     }

     /*recreate dropdown menus*/
     .navbar-left .dropdown-menu,
     .navbar-right .dropdown-menu,
     .navbar-left .open .dropdown-menu,
     .navbar-right .open .dropdown-menu {
        z-index: 1000;
        margin-top: 0 !important;
        float: left;
        position: absolute;
        top: 100%;
        left: -1px;
        min-width: 101%;
        list-style: none;

        background-color: #FFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        background-clip: padding-box;
        -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
     }

     /*revert usermenu dropdowns to absolute position*/
     #user-status + .btn-group .multiselect-container.dropdown-menu,
     #user-role + .btn-group .multiselect-container.dropdown-menu {
        top: 34px !important;
        position: absolute !important;
        right: 0;
        left: auto;
     }
}

/* Sidebar Responsive Changes
 * Introuduced a smaller version of the minified sidebar 
 */
@media (max-width: 900px) {

    /* Sidebar Minified State */
    body.sb-l-m #sidebar_left {
        width: 45px;
    }
    body.sb-l-m #sidebar_left:before {
        width: 45px;
    }
    /* Icon */
    body.sb-l-m .sidebar-menu > li > a > span:nth-child(1) {
        left: -1px;
        width: 45px;
        font-size: 18px;
    }

    /* Item title */
    body.sb-l-m .sidebar-menu > li > a > .sidebar-title {
        position: absolute;
        left: 45px;
        top: -4px;
        width: 180px;
        height: 44px;
        line-height: 44px;
        font-size: 14px;
        padding-left: 15px;
        border-left: 1px solid #222;
        background-color: #282d33;
    }

    /* Sub-Menus */
    body.sb-l-m .sidebar-menu > li > ul {
        position: absolute;
        left: 45px;
        top: 38px;
        width: 180px;
        height: auto;
        border-left: 1px solid #222;
        padding-bottom: 7px;
        overflow: hidden;
    }
    body.sb-l-m .sidebar-menu > li > ul > li ul {
        width: 180px;
    }

    /* first level menu item padding */
    body.sb-l-m .sidebar-menu li > ul > li > a {
        padding: 8px 20px 8px 20px;
    }

    /* multi level menu item padding */
    body.sb-l-m .sidebar-menu li > ul > li > ul > li a {
        padding: 8px 20px 8px 35px;
    }

    /* related page container modifications */
    body.sb-l-m #content_wrapper {
        margin-left: 45px;
    }

    /* If sidebar is hidden and minified */
    body.sb-l-c.sb-l-m #content_wrapper {
        margin-left: 0;
    }

    /* Navbar Branding Mini mode */
    body.sb-l-m .navbar-branding,
    body.sb-l-o .navbar-branding {
        max-width: 52px;
        margin-right: 0;
    }
    body.sb-l-m .navbar-brand,
    body.sb-l-o .navbar-brand {
        display: none;
    }


    /*hide sidebar user menu button when the sidebar is
    collapsed. As it's not accessible in this mode*/
    .sb-l-o .navbar .sidebar-menu-toggle,
    .sb-l-c .navbar .sidebar-menu-toggle {
        margin-left: 10px;
        display: none;
    }

}

/* Tray Changes */
/* Disable completely on resolutions <600 */
@media (max-width: 600px) {
    body.tray-rescale .tray-left,
    body.tray-rescale .tray-right {
        display: none;
    }
    body.tray-rescale .tray-center {
        padding-right: 13px !important;
    }
    body.tray-rescale #content.table-layout > div,
    body.tray-rescale #content.table-layout > section {
        padding: 10px 13px 40px !important;
    }
}

