/* ==========================================================================
   Defaults

   This file is exclusively intended for setting up variables
   Never add styles directly to this file
   ========================================================================== */

// Border radius
// -------------------------

$border-radius:     4px;


// Forms
// -------------------------

%input-style {
    background-color: #fff;
    outline: none;
    width: 100%;
    *width: 95%;
    height: 30px;
    padding: 4px 10px;
    border: 1px solid $hr-border;
    border-radius: $border-radius;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);

    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    &:focus {
        border-color: $form-blue;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba($form-blue,.6);
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba($form-blue,.6);
    }
}
