//
// Variables
// --------------------------------------------------

// == Define Directory/Path Structure
//
@root-path:		     "../../../../"; // Root/assets/skin/default-skin/less/ *here*
@assets-path:	     "../../../";
@img-path:	         "../../../img";
@fonts-path:         "../../../fonts";


//== Colors
//
//## Gray and brand colors for use across Bootstrap.

@white:                 #fff;
@muted:                 #999;
@transparent:           transparent;
@brand-light:           #fafafa;

@gray-darker:           lighten(#000, 13.5%); // #222
@gray-dark:             lighten(#000, 20%);   // #333
@gray:                  lighten(#000, 33.5%); // #555
@gray-light:            lighten(#000, 46.7%); // #777
@gray-lighter:          lighten(#000, 93.5%); // #eee

// default contextual colors
@brand-default:         #f0f0f0; 
@brand-primary:         #4a89dc; // blue
@brand-success:         #70ca63; // green
@brand-info:            #3bafda; // light blue
@brand-warning:         #f6bb42; // yellow
@brand-danger:          #e9573f; // red
@brand-alert:           #967adc; // purple 
@brand-system:          #37bc9b; // teal
@brand-dark:            #3b3f4f; // dark/black

//== Scaffolding
//
//## Settings for some of the most global styles.

//** Background color for `<body>`.
@body-bg:               #fff;
//** Global text color on `<body>`.
@text-color:            #666;

//** Global textual link color.
@link-color:            @brand-primary;
//** Link hover color set via `darken()` function.
@link-hover-color:      darken(@link-color, 15%);

//** Background color for `<#main>`.
@main-bg: 				#eee;        


//== Typography
//
//## Font, line-height, and color for body text, headings, and more.

@font-family-sans-serif:  "Open Sans", Helvetica, Arial, sans-serif;
@font-family-serif:       Georgia, "Times New Roman", Times, serif;
//** Default monospace fonts for `<code>`, `<kbd>`, and `<pre>`.
@font-family-monospace:   Menlo, Monaco, Consolas, "Courier New", monospace;
@font-family-base:        @font-family-sans-serif;

@font-weight-base:        400;

@font-size-base:          13px;
@font-size-large:         ceil((@font-size-base * 1.25)); // ~18px
@font-size-small:         ceil((@font-size-base * 0.85)); // ~12px

@font-size-h1:            floor((@font-size-base * 2.35)); // ~30px
@font-size-h2:            floor((@font-size-base * 1.85)); // ~24px
@font-size-h3:            ceil((@font-size-base * 1.35)); // ~18px
@font-size-h4:            ceil((@font-size-base * 1.1)); // ~15px
@font-size-h5:            @font-size-base; // ~ 13px(base)
@font-size-h6:            ceil((@font-size-base * 0.8)); // ~11px
@font-color-h6:           @muted; 

//** Unit-less `line-height` for use in components like buttons.
@line-height-base:        1.5; // 20/14
//** Computed "line-height" (`font-size` * `line-height`) for use with `margin`, `padding`, etc.
@line-height-computed:    floor((@font-size-base * @line-height-base)); // ~20px

//** By default, this inherits from the `<body>`.
@headings-font-family:    inherit;
@headings-font-weight:    600;
@headings-line-height:    1.1;
@headings-color:          inherit;

//** Headings small color
@headings-small-color:        @muted;

//** Media Object global font weight
@media-font-weight: 600;
@media-body-color: #999;
@media-heading-color: #555;


// //== Iconography
// //
// //## Specify custom location and filename of the included Glyphicons icon font. Useful for those including Bootstrap via Bower.

// //** Load fonts from this directory.
// @icon-font-path:          "../fonts/glyphicons/";
// //** File name for all font files.
// @icon-font-name:          "glyphicons-halflings-regular";
// //** Element ID within SVG icon file.
// @icon-font-svg-id:        "glyphicons_halflingsregular";


//== Components
//
//## Define common padding and border radius sizes and more. Values based on 14px text and 1.428 line-height (~20px to start).

@padding-base-vertical:     9px;
@padding-base-horizontal:   12px;

@padding-large-vertical:    10px;
@padding-large-horizontal:  16px;

@padding-small-vertical:    5px;
@padding-small-horizontal:  10px;

@padding-xs-vertical:       1px;
@padding-xs-horizontal:     5px;

@line-height-large:         1.33;
@line-height-small:         1.5;

@border-radius-base:        1px;
@border-radius-large:       3px;
@border-radius-small:       0px;


//** Global color for active items (e.g., navs or dropdowns).
@component-active-color:    #fff;
//** Global background color for active items (e.g., navs or dropdowns).
@component-active-bg:       @brand-primary;

//** Width of the `border` for generating carets that indicator dropdowns.
@caret-width-base:          4px;
//** Carets increase slightly in size for larger components.
@caret-width-large:         5px;


//== Tables
//
//## Customizes the `.table` component with basic values, each used across all table variations.

//** Padding for `<th>`s and `<td>`s.
@table-cell-padding:            9px;
//** Padding for cells in `.table-condensed`.
@table-condensed-cell-padding:  5px;

//** Default background color used for all tables.
@table-bg:                      transparent;
//** Background color used for `.table-striped`.
@table-bg-accent:               #f9f9f9;
//** Background color used for `.table-hover`.
@table-bg-hover:                #f5f5f5;
@table-bg-active:               @table-bg-hover;

//** Border color for table and cell borders.
@table-border-color:            #eee;


//== Buttons
//
//## For each of Bootstrap's buttons, define text, background and border color.

@btn-font-weight:                normal;

@btn-default-color:              #666;
@btn-default-bg:                 #f0f0f0;
@btn-default-border:             #ddd;

@btn-primary-color:              @white;
@btn-primary-bg:                 @brand-primary;
@btn-primary-border:             @brand-primary;

@btn-success-color:              @white;
@btn-success-bg:                 @brand-success;
@btn-success-border:             @brand-success;

@btn-info-color:                 @white;
@btn-info-bg:                    @brand-info;
@btn-info-border:                @brand-info;

@btn-warning-color:              @white;
@btn-warning-bg:                 @brand-warning;
@btn-warning-border:             @brand-warning;

@btn-danger-color:               @white;
@btn-danger-bg:                  @brand-danger;
@btn-danger-border:              @brand-danger;

@btn-alert-color:                @white;
@btn-alert-bg:                   @brand-alert;
@btn-alert-border:               @brand-alert;

@btn-system-color:               @white;
@btn-system-bg:                  @brand-system;
@btn-system-border:              @brand-system;

@btn-dark-color:                 @white;
@btn-dark-bg:                    @brand-dark;
@btn-dark-border:                @brand-dark;

@btn-link-disabled-color:        @gray-light;


//== Forms
//
//##

//** `<input>` background color
@input-bg:                       #fff;
//** `<input disabled>` background color
@input-bg-disabled:              #fafafa;

//** Text color for `<input>`s
@input-color:                    @gray;
//** `<input>` border color
@input-border:                   #ddd;
//** `<input>` border radius
@input-border-radius:            @border-radius-small;
//** Border color for inputs on focus
@input-border-focus:             lighten(@brand-primary, 10%);


//** Placeholder text color
@input-color-placeholder:        @gray-light;

//** Default `.form-control` height
@input-height-base:              (@line-height-computed + (@padding-base-vertical * 2) + 2);
//** Large `.form-control` height
@input-height-large:             (ceil(@font-size-large * @line-height-large) + (@padding-large-vertical * 2) + 2);
//** Small `.form-control` height
@input-height-small:             (floor(@font-size-small * @line-height-small) + (@padding-small-vertical * 2) + 2);

@legend-color:                   @gray-dark;
@legend-border-color:            #e5e5e5;


//** Background color for textual input addons
@input-group-addon-color:        @brand-primary;
//** Background color for textual input addons
@input-group-addon-bg:           #fafafa;
//** Border color for textual input addons
@input-group-addon-border-color: #ddd;


//== Dropdowns
//
//## Dropdown menu container and contents.

//** Background for the dropdown menu.
@dropdown-bg:                    #fff;
//** Dropdown menu `border-color`.
@dropdown-border:                rgba(0,0,0,.15);
//** Dropdown menu `border-color` **for IE8**.
@dropdown-fallback-border:       #ccc;
//** Divider color for between dropdown items.
@dropdown-divider-bg:            #e5e5e5;

//** Dropdown link text color.
@dropdown-link-color:            #666;
//** Hover color for dropdown links.
@dropdown-link-hover-color:      darken(#666, 5%);
//** Hover background for dropdown links.
@dropdown-link-hover-bg:         #f5f5f5;

//** Active dropdown menu item text color.
@dropdown-link-active-color:     @component-active-color;
//** Active dropdown menu item background color.
@dropdown-link-active-bg:        @component-active-bg;

//** Disabled dropdown menu item background color.
@dropdown-link-disabled-color:   @gray-light;

//** Text color for headers within dropdown menus.
@dropdown-header-color:          @gray-light;

//** Deprecated `@dropdown-caret-color` as of v3.1.0
@dropdown-caret-color:           #000;


//-- Z-index master list
//
// Warning: Avoid customizing these values. They're used for a bird's eye view
// of components dependent on the z-axis and are designed to all work together.
//
// Note: These variables are not generated into the Customizer.

@zindex-navbar:            1000;
@zindex-dropdown:          1000;
@zindex-popover:           1060;
@zindex-tooltip:           1070;
@zindex-navbar-fixed:      1030;
@zindex-modal-background:  1040;
@zindex-modal:             1050;


//== Media queries breakpoints
//
//## Define the breakpoints at which your layout will change, adapting to different screen sizes.

// Extra small screen / phone
//** Deprecated `@screen-xs` as of v3.0.1
@screen-xs:                  480px;
//** Deprecated `@screen-xs-min` as of v3.2.0
@screen-xs-min:              @screen-xs;
//** Deprecated `@screen-phone` as of v3.0.1
@screen-phone:               @screen-xs-min;

// Small screen / tablet
//** Deprecated `@screen-sm` as of v3.0.1
@screen-sm:                  768px;
@screen-sm-min:              @screen-sm;
//** Deprecated `@screen-tablet` as of v3.0.1
@screen-tablet:              @screen-sm-min;

// Medium screen / desktop
//** Deprecated `@screen-md` as of v3.0.1
@screen-md:                  992px;
@screen-md-min:              @screen-md;
//** Deprecated `@screen-desktop` as of v3.0.1
@screen-desktop:             @screen-md-min;

// Large screen / wide desktop
//** Deprecated `@screen-lg` as of v3.0.1
@screen-lg:                  1140px;
@screen-lg-min:              @screen-lg;
//** Deprecated `@screen-lg-desktop` as of v3.0.1
@screen-lg-desktop:          @screen-lg-min;

// XLarge screen / extra wide desktop
@screen-xl:                  1400px;
@screen-xl-min:              @screen-xl;
@screen-xl-desktop:          @screen-xl-min;

// So media queries don't overlap when required, provide a maximum
@screen-xs-max:              (@screen-sm-min - 1);
@screen-sm-max:              (@screen-md-min - 1);
@screen-md-max:              (@screen-lg-min - 1);
@screen-lg-max:              (@screen-xl-min - 1);


// Mobile View Breakpoint - Currently set to 0 as to disable
@screen-mobile:              0px;


//== Grid system
//
//## Define your custom responsive grid.

//** Number of columns in the grid.
@grid-columns:              12;
//** Padding between columns. Gets divided in half for the left and right.
@grid-gutter-width:         22px;
// Navbar collapse
//** Point at which the navbar becomes uncollapsed. Setting to "@screen-xs-min"
// will prevent the navbar from ever entering mobile mode.
@grid-float-breakpoint:     @screen-xs-min;
//** Point at which the navbar begins collapsing.
@grid-float-breakpoint-max: (@grid-float-breakpoint - 1);


//== Container sizes
//
//## Define the maximum width of `.container` for different screen sizes.

// Small screen / tablet
@container-tablet:             ((720px + @grid-gutter-width));
//** For `@screen-sm-min` and up.
@container-sm:                 @container-tablet;

// Medium screen / desktop
@container-desktop:            ((940px + @grid-gutter-width));
//** For `@screen-md-min` and up.
@container-md:                 @container-desktop;

// Large screen / wide desktop
@container-large-desktop:      ((1040px + @grid-gutter-width));
//** For `@screen-lg-min` and up.
@container-lg:                 @container-large-desktop;

// Extra Large screen / Extra wide desktop
@container-xlarge-desktop:      ((1280px + @grid-gutter-width));
//** For `@screen-xl-min` and up.
@container-xl:                 @container-xlarge-desktop;


//== Content Tray Navs
//
//## Define colors used in content tray navigations(".tray-nav")
@tray-nav-active-icon: @brand-primary;
@tray-nav-active-border: @brand-primary;


//== Navbar
//
//##

// Basics of a navbar
@navbar-height:                    50px;
@navbar-margin-bottom:             @line-height-computed;
@navbar-border-radius:             @border-radius-base;
@navbar-padding-horizontal:        floor((@grid-gutter-width / 2));
@navbar-padding-vertical:          ((@navbar-height - @line-height-computed) / 2);
@navbar-collapse-max-height:       340px;

@navbar-default-color:             #777;
@navbar-default-bg:                #f8f8f8;
@navbar-default-border:            darken(@navbar-default-bg, 6.5%);

// Navbar links
@navbar-default-link-color:                #777;
@navbar-default-link-hover-color:          #333;
@navbar-default-link-hover-bg:             transparent;
@navbar-default-link-active-color:         #555;
@navbar-default-link-active-bg:            darken(@navbar-default-bg, 6.5%);
@navbar-default-link-disabled-color:       #ccc;
@navbar-default-link-disabled-bg:          transparent;

// Navbar brand label
@navbar-default-brand-color:               @navbar-default-link-color;
@navbar-default-brand-hover-color:         darken(@navbar-default-brand-color, 10%);
@navbar-default-brand-hover-bg:            transparent;

// Navbar toggle
@navbar-default-toggle-hover-bg:           #ddd;
@navbar-default-toggle-icon-bar-bg:        #888;
@navbar-default-toggle-border-color:       #ddd;


// Inverted navbar
// Reset inverted navbar basics
@navbar-inverse-color:                      @gray-light;
@navbar-inverse-bg:                         #222;
@navbar-inverse-border:                     darken(@navbar-inverse-bg, 10%);

// Inverted navbar links
@navbar-inverse-link-color:                 @gray-light;
@navbar-inverse-link-hover-color:           #fff;
@navbar-inverse-link-hover-bg:              transparent;
@navbar-inverse-link-active-color:          @navbar-inverse-link-hover-color;
@navbar-inverse-link-active-bg:             darken(@navbar-inverse-bg, 10%);
@navbar-inverse-link-disabled-color:        #444;
@navbar-inverse-link-disabled-bg:           transparent;

// Inverted navbar brand label
@navbar-inverse-brand-color:                @navbar-inverse-link-color;
@navbar-inverse-brand-hover-color:          #fff;
@navbar-inverse-brand-hover-bg:             transparent;

// Inverted navbar toggle
@navbar-inverse-toggle-hover-bg:            #333;
@navbar-inverse-toggle-icon-bar-bg:         #fff;
@navbar-inverse-toggle-border-color:        #333;


//== Navs
//
//##

//=== Shared nav styles
@nav-link-padding:                          10px 15px;
@nav-link-hover-bg:                         @gray-lighter;

@nav-disabled-link-color:                   @gray-light;
@nav-disabled-link-hover-color:             @gray-light;

@nav-open-link-hover-color:                 #fff;

//== Tabs
@nav-tabs-border-color:                     #ddd;

@nav-tabs-link-hover-border-color:          @gray-lighter;

@nav-tabs-active-link-hover-bg:             transparent;
@nav-tabs-active-link-hover-color:          @gray;
@nav-tabs-active-link-hover-border-color:   #ddd;

@nav-tabs-justified-link-border-color:            #ddd;
@nav-tabs-justified-active-link-border-color:     @body-bg;

//== Pills
@nav-pills-border-radius:                   @border-radius-base;
@nav-pills-active-link-hover-bg:            @component-active-bg;
@nav-pills-active-link-hover-color:         @component-active-color;


//== Pagination
//
//##

@pagination-color:                     @link-color;
@pagination-bg:                        #fff;
@pagination-border:                    #ddd;

@pagination-hover-color:               @link-hover-color;
@pagination-hover-bg:                  @gray-lighter;
@pagination-hover-border:              #ddd;

@pagination-active-color:              #fff;
@pagination-active-bg:                 @brand-primary;
@pagination-active-border:             @brand-primary;

@pagination-disabled-color:            @gray-light;
@pagination-disabled-bg:               #fff;
@pagination-disabled-border:           #ddd;


//== Pager
//
//##

@pager-bg:                             @pagination-bg;
@pager-border:                         @pagination-border;
@pager-border-radius:                  15px;

@pager-hover-bg:                       @pagination-hover-bg;

@pager-active-bg:                      @pagination-active-bg;
@pager-active-color:                   @pagination-active-color;

@pager-disabled-color:                 @pagination-disabled-color;


//== Jumbotron
//
//##

@jumbotron-padding:              30px;
@jumbotron-color:                inherit;
@jumbotron-bg:                   @gray-lighter;
@jumbotron-heading-color:        inherit;
@jumbotron-font-size:            ceil((@font-size-base * 1.5));


//== Form states and alerts
//
//## Define colors for form feedback states and, by default, alerts.

@state-light-text:               @brand-light;
@state-light-bg:                 lighten(@state-light-text, 10%);
@state-light-border:             darken(spin(@state-light-bg, -10), 5%);

@state-default-text:             @brand-default;
@state-default-bg:               lighten(@state-default-text, 10%);
@state-default-border:           darken(spin(@state-default-bg, -10), 5%);

@state-primary-text:             @brand-primary;
@state-primary-bg:               lighten(@state-primary-text, 10%);
@state-primary-border:           darken(spin(@state-primary-bg, -10), 5%);

@state-success-text:             @brand-success;
@state-success-bg:               lighten(@state-success-text, 10%);
@state-success-border:           darken(spin(@state-success-bg, -10), 5%);

@state-info-text:                @brand-info;
@state-info-bg:                  lighten(@state-info-text, 10%);
@state-info-border:              darken(spin(@state-info-bg, -10), 7%);

@state-warning-text:             @brand-warning;
@state-warning-bg:               lighten(@state-warning-text, 10%);
@state-warning-border:           darken(spin(@state-warning-bg, -10), 5%);

@state-danger-text:              @brand-danger;
@state-danger-bg:                lighten(@state-danger-text, 13%);
@state-danger-border:            darken(spin(@state-danger-bg, -10), 5%);

@state-alert-text:               @brand-alert;
@state-alert-bg:                 lighten(@state-alert-text, 7%);
@state-alert-border:             darken(spin(@state-alert-bg, -10), 7%);

@state-system-text:              @brand-system;
@state-system-bg:                lighten(@state-system-text, 10%);
@state-system-border:            darken(spin(@state-system-bg, -10), 5%);

@state-dark-text:                @brand-dark;
@state-dark-bg:                  lighten(@state-dark-text, 30%);
@state-dark-border:              darken(spin(@state-dark-bg, -10), 5%);



//== Tooltips
//
//##

//** Tooltip max width
@tooltip-max-width:           200px;
//** Tooltip text color
@tooltip-color:               #fff;
//** Tooltip background color
@tooltip-bg:                  #000;
@tooltip-opacity:             .9;

//** Tooltip arrow width
@tooltip-arrow-width:         5px;
//** Tooltip arrow color
@tooltip-arrow-color:         @tooltip-bg;


//== Popovers
//
//##

//** Popover body background color
@popover-bg:                          #fff;
//** Popover maximum width
@popover-max-width:                   276px;
//** Popover border color
@popover-border-color:                rgba(0,0,0,.2);
//** Popover fallback border color
@popover-fallback-border-color:       #ccc;

//** Popover title background color
@popover-title-bg:                    darken(@popover-bg, 3%);

//** Popover arrow width
@popover-arrow-width:                 10px;
//** Popover arrow color
@popover-arrow-color:                 #fff;

//** Popover outer arrow width
@popover-arrow-outer-width:           (@popover-arrow-width + 1);
//** Popover outer arrow color
@popover-arrow-outer-color:           fadein(@popover-border-color, 5%);
//** Popover outer arrow fallback color
@popover-arrow-outer-fallback-color:  darken(@popover-fallback-border-color, 20%);


//== Labels - also shared by Badges
//
//##

//** Muted label background color
@label-muted-bg:              #AAA;

//** Default label background color
@label-default-bg:            @gray-light;

//** Primary label background color
@label-primary-bg:            @brand-primary;

//** Success label background color
@label-success-bg:            @brand-success;

//** Info label background color
@label-info-bg:               @brand-info;

//** Warning label background color
@label-warning-bg:            @brand-warning;

//** Danger label background color
@label-danger-bg:             @brand-danger;

//** Alert label background color
@label-alert-bg:              @brand-alert;

//** System label background color
@label-system-bg:             @brand-system;

//** Dark label background color
@label-dark-bg:               @brand-dark;

//** Default label text color
@label-color:                 #fff;
//** Default text color of a linked label
@label-link-hover-color:      #fff;


//== Modals
//
//##

//** Padding applied to the modal body
@modal-inner-padding:         15px;

//** Padding applied to the modal title
@modal-title-padding:         15px;
//** Modal title line-height
@modal-title-line-height:     @line-height-base;

//** Background color of modal content area
@modal-content-bg:                             #fff;
//** Modal content border color
@modal-content-border-color:                   rgba(0,0,0,.2);
//** Modal content border color **for IE8**
@modal-content-fallback-border-color:          #999;

//** Modal backdrop background color
@modal-backdrop-bg:           #000;
//** Modal backdrop opacity
@modal-backdrop-opacity:      .5;
//** Modal header border color
@modal-header-border-color:   #e5e5e5;
//** Modal footer border color
@modal-footer-border-color:   @modal-header-border-color;

@modal-lg:                    900px;
@modal-md:                    600px;
@modal-sm:                    300px;


//== Alerts
//
//## Define alert colors, border radius, and padding.

@alert-padding:               15px;
@alert-border-radius:         @border-radius-base;
@alert-font-size:             14px;
@alert-link-font-weight:      bold;

@alert-default-bg:            @brand-default;
@alert-default-text:          #666;

@alert-primary-bg:            @brand-primary;
@alert-primary-text:          @white;

@alert-success-bg:            @brand-success;
@alert-success-text:          @white;

@alert-info-bg:               @brand-info;
@alert-info-text:             @white;

@alert-warning-bg:            @brand-warning;
@alert-warning-text:          @white;

@alert-danger-bg:             @brand-danger;
@alert-danger-text:           @white;

@alert-alert-bg:              @brand-alert;
@alert-alert-text:            @white;

@alert-system-bg:             @brand-system;
@alert-system-text:           @white;

@alert-dark-bg:               @brand-dark;
@alert-dark-text:             @white;

//== Progress bars
//
//##

//** Background color of the whole progress component
@progress-bg:                 #ececec;

//** Progress bar text color
@progress-bar-color:          #fff;

//** Default progress bar color
@progress-bar-bg:             #999999;
@progress-bar-default-bg:     #999999;

//** Primary progress bar color
@progress-bar-primary-bg:     @brand-primary;

//** Success progress bar color
@progress-bar-success-bg:     @brand-success;

//** Info progress bar color
@progress-bar-info-bg:        @brand-info;

//** Warning progress bar color
@progress-bar-warning-bg:     @brand-warning;

//** Danger progress bar color
@progress-bar-danger-bg:      @brand-danger;

//** Alert progress bar color
@progress-bar-alert-bg:       @brand-alert;

//** System progress bar color
@progress-bar-system-bg:      @brand-system;

//** Dark progress bar color
@progress-bar-dark-bg:        @brand-dark;


//== List group
//
//##

//** Background color on `.list-group-item`
@list-group-bg:                 #fff;
//** `.list-group-item` border color
@list-group-border:             #e7e7e7;
//** List group border radius
@list-group-border-radius:      2px;

//** Background color of single list items on hover
@list-group-hover-bg:           #f5f5f5;
//** Text color of active list items
@list-group-active-color:       @component-active-color;
//** Background color of active list items
@list-group-active-bg:          @component-active-bg;
//** Border color of active list elements
@list-group-active-border:      @list-group-active-bg;
//** Text color for content within active list items
@list-group-active-text-color:  lighten(@list-group-active-bg, 40%);

//** Text color of disabled list items
@list-group-disabled-color:      @gray-light;
//** Background color of disabled list items
@list-group-disabled-bg:         @gray-lighter;
//** Text color for content within disabled list items
@list-group-disabled-text-color: @list-group-disabled-color;

@list-group-link-color:         @brand-primary;
@list-group-link-hover-color:   @list-group-link-color;
@list-group-link-heading-color: #333;


//== Panels
//
//##

@panel-bg:                     #fff;

// Panel Header
@panel-heading-padding:        0 8px;
@panel-heading-font-color:     #666;
@panel-heading-font-size:      13px;
@panel-heading-font-weight:    600;
@panel-heading-bg:             #fafafa;
@panel-heading-border-color:   #eee;

// Panel Body
@panel-body-padding:          15px;

// Panel Footer
@panel-footer-bg:             #fafafa;
@panel-footer-padding:        10px 15px;

// Panel Border Settings
@panel-border-color:          #e5e5e5; // Contextual classes apply colored borders. ie: ".panel-info"
@panel-border-radius:         2px;
@panel-inner-border:          #eee;

// Panel Contextuals
@panel-default-text:          @gray-dark;
@panel-default-border:        #ddd;
@panel-default-heading-bg:    #f5f5f5;

@panel-primary-text:          #fff;
@panel-primary-border:        @brand-primary;
@panel-primary-heading-bg:    @brand-primary;

@panel-success-text:          @white;
@panel-success-border:        @brand-success;
@panel-success-heading-bg:    @brand-success;

@panel-info-text:             @white;
@panel-info-border:           @brand-info;
@panel-info-heading-bg:       @brand-info;

@panel-warning-text:          @white;
@panel-warning-border:        @brand-warning;
@panel-warning-heading-bg:    @brand-warning;

@panel-danger-text:           @white;
@panel-danger-border:         @brand-danger;
@panel-danger-heading-bg:     @brand-danger;

@panel-alert-text:            @white;
@panel-alert-border:          @brand-alert;
@panel-alert-heading-bg:      @brand-alert;

@panel-system-text:           @white;
@panel-system-border:         @brand-system;
@panel-system-heading-bg:     @brand-system;

@panel-dark-text:             @white;
@panel-dark-border:           @brand-dark;
@panel-dark-heading-bg:       @brand-dark;

//== Thumbnails
//
//##

//** Padding around the thumbnail image
@thumbnail-padding:           4px;
//** Thumbnail background color
@thumbnail-bg:                @body-bg;
//** Thumbnail border color
@thumbnail-border:            #ddd;
//** Thumbnail border radius
@thumbnail-border-radius:     @border-radius-base;

//** Custom text color for thumbnail captions
@thumbnail-caption-color:     @text-color;
//** Padding around the thumbnail caption
@thumbnail-caption-padding:   9px;


//== Wells
//
//##

@well-bg:                     #f5f5f5;
@well-border:                 darken(@well-bg, 7%);


//== Badges
//
//##

@badge-color:                 #fff;
//** Linked badge text color on hover
@badge-link-hover-color:      #fff;
@badge-bg:                    @gray-light;

//** Badge text color in active nav link
@badge-active-color:          @link-color;
//** Badge background color in active nav link
@badge-active-bg:             #fff;

@badge-font-size:             12px;
@badge-font-weight:           600;
@badge-line-height:           1;
@badge-border-radius:         10px;


//== Breadcrumbs
//
//##

@breadcrumb-padding-vertical:   8px;
@breadcrumb-padding-horizontal: 15px;
//** Breadcrumb background color
@breadcrumb-bg:                 #f5f5f5;
//** Breadcrumb text color
@breadcrumb-color:              #ccc;
//** Text color of current page in the breadcrumb
@breadcrumb-active-color:       @gray-light;
//** Textual separator for between breadcrumb elements
@breadcrumb-separator:          "/";


//== Carousel
//
//##

@carousel-text-shadow:                        0 1px 2px rgba(0,0,0,.6);

@carousel-control-color:                      #fff;
@carousel-control-width:                      15%;
@carousel-control-opacity:                    .5;
@carousel-control-font-size:                  20px;

@carousel-indicator-active-bg:                #fff;
@carousel-indicator-border-color:             #fff;

@carousel-caption-color:                      #fff;


//== Close
//
//##

@close-font-weight:           bold;
@close-color:                 #000;
@close-text-shadow:           0 1px 0 #fff;


//== Code
//
//##

@code-color:                  #c7254e;
@code-bg:                     #f9f2f4;

@kbd-color:                   #fff;
@kbd-bg:                      #333;

@pre-bg:                      #eaeef0;
@pre-color:                   @gray-dark;
@pre-border-color:            #cbd6d6;
@pre-scrollable-max-height:   340px;


//== Type
//
//##

//** Horizontal offset for forms and lists.
@component-offset-horizontal: 180px;
//** Text muted color
@text-muted:                  @muted;
//** Abbreviations and acronyms border color
@abbr-border-color:           @gray-light;
//** Blockquote small color
@blockquote-small-color:      @gray-light;
//** Blockquote font size
@blockquote-font-size:        (@font-size-base * 1.25);
//** Blockquote border color
@blockquote-border-color:     @gray-lighter;
//** Page header border color
@page-header-border-color:    #DDD;
//** Width of horizontal description list titles
@dl-horizontal-offset:        @component-offset-horizontal;
//** Horizontal line color.
@hr-border:                   @gray-lighter;


