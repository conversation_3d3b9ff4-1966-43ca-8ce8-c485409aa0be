/* ==============================================
   Content Trays
     A. Trays
     B. Tray Sizes
     C. Tray Bin
     D. Tray Navigation
     E. Tray Hover Settings
=================================================
   A. Content Trays
================================================= */

/*tray table layout*/
#content.table-layout {
    padding: 0;
}

#content.table-layout > div,
#content.table-layout > section {
    padding: 25px 20px 50px;
}

/*trays*/
.tray-left,
.tray-right {
    position: relative;
    width: 250px;
    min-height: 100%;
    border-right: 1px solid #DDD;
    background: #f5f5f5;
    padding: 15px;
}

/*Right tray*/
.tray-right {
    border-left: 1px solid #DDD;
    border-right: 0;
}

/*Top tray*/
.tray-top {
    position: relative;
    width: 100%;
    min-height: 150px;
    border-bottom: 1px solid #DDD;
    background-color: #f2f2f2;
    padding: 15px;
}
.tray-top.light {
    background-color: #fafafa;
}

/*===============================================
   B. Tray Sizes
================================================= */
.tray200 {
    width: 200px;
}
.tray225 {
    width: 225px;
}
.tray250 {
    width: 250px;
}
.tray250 {
    width: 270px;
}
.tray290 {
    width: 290px;
}
.tray320 {
    width: 320px;
}
.tray350 {
    width: 350px;
}
.tray400 {
    width: 400px;
}

/*===============================================
   C. Tray Bin
================================================= */

/*a dashed bin great for organizing buttons and links*/
.tray-bin {
    padding: 7px;
    border: 1px dashed #CCC;
    background: #eee;
    min-height: 65px;
    margin-bottom: 20px;
}

/*If navbar has a contextual bg we make menu links white*/
.tray-bin div[class*='col-'] {
    padding-left: 5px;
    padding-right: 5px;
}

/*traybin divider text. Used primarily in li navs*/
.tray-bin .nav-label {
    text-align: center;
    font-size: 12px;
    color: @muted;
    padding-left: 5px;
    margin-top: 20px;
    margin-bottom: 10px;
}

/*===============================================
   D. Tray Navigation
================================================= */

/* item link */
ul.tray-nav li a {
    width: 100%;
    padding: 11px 15px 11px 30px;
    color: #999;
    font-size: 13px;
    background: #f2f2f2;
    border-top: 1px solid #DDD;
}

/* item icon */
ul.tray-nav li a .fa {
    padding-right: 18px;
}

/* active item  */
ul.tray-nav li.active a {
    color: #666;
    background: #fbfbfb;
    transition: all 0.3s ease;
}

/* active item icon */
ul.tray-nav li.active a .fa {
    color:@tray-nav-active-icon;
}

/*Tray nav style option - Arrow*/

/* active item bottom border */
ul.tray-nav.tray-nav-arrow li.active:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: 1;
    top: 1px;
    left: 0;
    border-bottom: 1px solid #DDD;
}
ul.tray-nav.tray-nav-arrow li.active a:before,
ul.tray-nav.tray-nav-arrow li.active a:after {
    content: "";
    position: absolute;
    top: 0;
    left: 100%;
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-left: 20px solid;
    border-left-color: #fbfbfb;
    border-top: 20px solid transparent;
    border-bottom: 20px solid transparent;
    transition: all 0.3s ease;
}
ul.tray-nav.tray-nav-arrow li.active a:before {
    border-left-color: #888;
}

/* item hover */
ul.tray-nav.tray-nav-arrow li:hover a:after {
    border-left-color: #f8f8f8;
}

/*settings for arrows when used on a right aligned tray (.tray-right)*/

/* active item bottom border */
.tray-right ul.tray-nav.tray-nav-arrow li.active:before {
    width: 100%;
    left: auto;
    right: 0;
}
.tray-right ul.tray-nav.tray-nav-arrow li.active a:before,
.tray-right ul.tray-nav.tray-nav-arrow li.active a:after {
    left: auto;
    right: 100%;
    border-right: 20px solid;
    border-left-color: transparent;
    border-right-color: #fbfbfb;
}
.tray-right ul.tray-nav.tray-nav-arrow li.active a:before {
    border-left-color: transparent;
    border-right-color: #888;
}

/* item hover */
.tray-right ul.tray-nav.tray-nav-arrow li:hover a:after {
    border-left-color: transparent;
    border-right-color: #f8f8f8;
}

/*Tray nav style option - Bordered*/
.tray-nav.tray-nav-border li {
    position: relative;
}
.tray-nav.tray-nav-border li a {
    font-size: 14px;
    padding: 12px 15px 12px 30px;
}
.tray-nav.tray-nav-border li:after {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #CCC;
}
.tray-nav.tray-nav-border li.active:after {
    background: @tray-nav-active-border;
}
.tray-nav.tray-nav-border li:hover:after {
    background: #999;
}

/*modifcation for right side trays (.tray-right)*/
.tray-right .tray-nav.tray-nav-border li:after {
    left: 0;
    right: auto;
}

/*border skin contextuals*/
.tray-nav.tray-nav-border li.active.nav-primary:after {
    background: @brand-primary;
}
.tray-nav.tray-nav-border li.active.nav-success:after {
    background: @brand-success;
}
.tray-nav.tray-nav-border li.active.nav-info:after {
    background: @brand-info;
}
.tray-nav.tray-nav-border li.active.nav-warning:after {
    background: @brand-warning;
}
.tray-nav.tray-nav-border li.active.nav-danger:after {
    background: @brand-danger;
}
.tray-nav.tray-nav-border li.active.nav-alert:after {
    background: @brand-alert;
}
.tray-nav.tray-nav-border li.active.nav-system:after {
    background: @brand-system;
}
.tray-nav.tray-nav-border li.active.nav-dark:after {
    background: @brand-dark;
}


/*===============================================
   E. Tray Hover Settings

   At <1000 window width javascript will add a 
   "tray-rescale" class to the document body. This
   will shift the menu over out of sight and
   expand it only when the user hovers over the 
   portion that's still visible
================================================= */
body.tray-rescale .tray-left,
body.tray-rescale .tray-right {
    position: fixed;
    z-index: 1;
    opacity: 0.5;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
}
body.tray-rescale .tray-left,
body.tray-rescale .tray-right {right: -275px;}

body.tray-rescale .tray-left {
    border-left: 1px solid #DDD;
}

/* adjust center tray to fill window width add needed
 padding to offset the partially hidden tray */
body.tray-rescale .tray-center {
    width: 100%;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    padding-right: 58px !important;
}
body.tray-rescale .tray-top + .tray-center {
    padding-right: inherit !important;
}
/* adjust depending on tray size */
body.tray-rescale .tray.tray200 {
    right: -155px;
}
body.tray-rescale .tray.tray225 {
    right: -180px;
}
body.tray-rescale .tray.tray250 {
    right: -205px;
}
body.tray-rescale .tray.tray270 {
    right: -225px;
}
body.tray-rescale .tray.tray290 {
    right: -245px;
}
body.tray-rescale .tray.tray320 {
    right: -275px;
}
body.tray-rescale .tray.tray350 {
    right: -305px;
}
body.tray-rescale .tray.tray400 {
    right: -355px;
}

/* on hover open the menus */
body.tray-rescale .tray-left:hover,
body.tray-rescale .tray-right:hover {
    opacity: 1;
    z-index: 999;
}

body.tray-rescale .tray-left:hover,
body.tray-rescale .tray-right:hover {right: 0px;}


/* adjust any bootstrap affix settings if they exist */
body.tray-rescale .tray .tray-nav.affix,
body.tray-rescale .tray .affix-pane.affix {
    top: auto;
}

// Changes added via updates
// Update v1.2
//

// Tray Responsive Changes
// Hide the tray completely iff window is <600 px

/* Disable completely on resolutions <600 */
@media (max-width: 600px) {
    body.tray-rescale .tray-left,
    body.tray-rescale .tray-right {
        display: none;
    }
    body.tray-rescale .tray-center {
        padding-right: 13px !important;
    }
    body.tray-rescale #content.table-layout > div,
    body.tray-rescale #content.table-layout > section {
        padding: 10px 13px 40px !important;
    }
}
