<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">

	<title>DataTables example - HTML5 data-* attributes</title>
	<link rel="stylesheet" type="text/css" href="../../media/css/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../resources/demo.css">
	<style type="text/css" class="init">

	</style>
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.dataTables.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/demo.js"></script>
	<script type="text/javascript" language="javascript" class="init">


$(document).ready(function() {
	$('#example').dataTable();
} );


	</script>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>DataTables example <span>HTML5 data-* attributes</span></h1>

			<div class="info">
				<p>DataTables can use different data for different actions (display, ordering and searching) which can
				be immensely powerful for transforming data in the display to be intuitive for the end user, while
				using different, or more complex data, for other actions. For example, if a table contains a formatted
				telephone number in the format <code>xxx-xxxx</code>, intuitively a user might search for the number
				but without a dash. Using orthogonal data for searching allows both forms of the telephone number to be
				used, while only the nicely formatted number is displayed in the table.</p>

				<p>One method in which DataTables can obtain this orthogonal data for its different actions is through
				<a href=
				"http://www.w3.org/html/wg/drafts/html/master/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes">
				custom HTML5 data attributes</a>. DataTables will automatically detect four different attributes on the
				HTML elements:</p>

				<ul class="markdown">
					<li><code>data-sort</code> or <code>data-order</code> - for ordering data</li>
					<li><code>data-filter</code> or <code>data-search</code> - for search data</li>
				</ul>

				<p>This example shows the use of <code>data-sort</code> and <code>data-filter</code> attributes. In
				this case the first column has been formatted so the first name has abbreviated, but the full name is
				still searchable (search for "Bruno" for example). Additionally, although the last column contains
				non-numeric data in it (<code>/y</code>) the column will correctly order numerically as the
				<code>data-sort</code> / <code>data-order</code> attribute is set on the column with plain numeric
				data.</p>
			</div>

			<table id="example" class="display" cellspacing="0" width="100%">
				<thead>
					<tr>
						<th>Name</th>
						<th>Position</th>
						<th>Office</th>
						<th>Age</th>
						<th>Start date</th>
						<th>Salary</th>
					</tr>
				</thead>

				<tfoot>
					<tr>
						<th>Name</th>
						<th>Position</th>
						<th>Office</th>
						<th>Age</th>
						<th>Start date</th>
						<th>Salary</th>
					</tr>
				</tfoot>

				<tbody>
					<tr>
						<td data-search="Tiger Nixon">T. Nixon</td>
						<td>System Architect</td>
						<td>Edinburgh</td>
						<td>61</td>
						<td data-order="**********">Mon 25th Apr 11</td>
						<td data-order="320800">$320,800/y</td>
					</tr>
					<tr>
						<td data-search="Garrett Winters">G. Winters</td>
						<td>Accountant</td>
						<td>Tokyo</td>
						<td>63</td>
						<td data-order="**********">Mon 25th Jul 11</td>
						<td data-order="170750">$170,750/y</td>
					</tr>
					<tr>
						<td data-search="Ashton Cox">A. Cox</td>
						<td>Junior Technical Author</td>
						<td>San Francisco</td>
						<td>66</td>
						<td data-order="**********">Mon 12th Jan 09</td>
						<td data-order="86000">$86,000/y</td>
					</tr>
					<tr>
						<td data-search="Cedric Kelly">C. Kelly</td>
						<td>Senior Javascript Developer</td>
						<td>Edinburgh</td>
						<td>22</td>
						<td data-order="**********">Thu 29th Mar 12</td>
						<td data-order="433060">$433,060/y</td>
					</tr>
					<tr>
						<td data-search="Airi Satou">A. Satou</td>
						<td>Accountant</td>
						<td>Tokyo</td>
						<td>33</td>
						<td data-order="**********">Fri 28th Nov 08</td>
						<td data-order="162700">$162,700/y</td>
					</tr>
					<tr>
						<td data-search="Brielle Williamson">B. Williamson</td>
						<td>Integration Specialist</td>
						<td>New York</td>
						<td>61</td>
						<td data-order="**********">Sun 2nd Dec 12</td>
						<td data-order="372000">$372,000/y</td>
					</tr>
					<tr>
						<td data-search="Herrod Chandler">H. Chandler</td>
						<td>Sales Assistant</td>
						<td>San Francisco</td>
						<td>59</td>
						<td data-order="**********">Mon 6th Aug 12</td>
						<td data-order="137500">$137,500/y</td>
					</tr>
					<tr>
						<td data-search="Rhona Davidson">R. Davidson</td>
						<td>Integration Specialist</td>
						<td>Tokyo</td>
						<td>55</td>
						<td data-order="**********">Thu 14th Oct 10</td>
						<td data-order="327900">$327,900/y</td>
					</tr>
					<tr>
						<td data-search="Colleen Hurst">C. Hurst</td>
						<td>Javascript Developer</td>
						<td>San Francisco</td>
						<td>39</td>
						<td data-order="1252969200">Tue 15th Sep 09</td>
						<td data-order="205500">$205,500/y</td>
					</tr>
					<tr>
						<td data-search="Sonya Frost">S. Frost</td>
						<td>Software Engineer</td>
						<td>Edinburgh</td>
						<td>23</td>
						<td data-order="1229126400">Sat 13th Dec 08</td>
						<td data-order="103600">$103,600/y</td>
					</tr>
					<tr>
						<td data-search="Jena Gaines">J. Gaines</td>
						<td>Office Manager</td>
						<td>London</td>
						<td>30</td>
						<td data-order="1229644800">Fri 19th Dec 08</td>
						<td data-order="90560">$90,560/y</td>
					</tr>
					<tr>
						<td data-search="Quinn Flynn">Q. Flynn</td>
						<td>Support Lead</td>
						<td>Edinburgh</td>
						<td>22</td>
						<td data-order="1362268800">Sun 3rd Mar 13</td>
						<td data-order="342000">$342,000/y</td>
					</tr>
					<tr>
						<td data-search="Charde Marshall">C. Marshall</td>
						<td>Regional Director</td>
						<td>San Francisco</td>
						<td>36</td>
						<td data-order="1224111600">Thu 16th Oct 08</td>
						<td data-order="470600">$470,600/y</td>
					</tr>
					<tr>
						<td data-search="Haley Kennedy">H. Kennedy</td>
						<td>Senior Marketing Designer</td>
						<td>London</td>
						<td>43</td>
						<td data-order="1355788800">Tue 18th Dec 12</td>
						<td data-order="313500">$313,500/y</td>
					</tr>
					<tr>
						<td data-search="Tatyana Fitzpatrick">T. Fitzpatrick</td>
						<td>Regional Director</td>
						<td>London</td>
						<td>19</td>
						<td data-order="1268784000">Wed 17th Mar 10</td>
						<td data-order="385750">$385,750/y</td>
					</tr>
					<tr>
						<td data-search="Michael Silva">M. Silva</td>
						<td>Marketing Designer</td>
						<td>London</td>
						<td>66</td>
						<td data-order="1353974400">Tue 27th Nov 12</td>
						<td data-order="198500">$198,500/y</td>
					</tr>
					<tr>
						<td data-search="Paul Byrd">P. Byrd</td>
						<td>Chief Financial Officer (CFO)</td>
						<td>New York</td>
						<td>64</td>
						<td data-order="1276038000">Wed 9th Jun 10</td>
						<td data-order="725000">$725,000/y</td>
					</tr>
					<tr>
						<td data-search="Gloria Little">G. Little</td>
						<td>Systems Administrator</td>
						<td>New York</td>
						<td>59</td>
						<td data-order="1239318000">Fri 10th Apr 09</td>
						<td data-order="237500">$237,500/y</td>
					</tr>
					<tr>
						<td data-search="Bradley Greer">B. Greer</td>
						<td>Software Engineer</td>
						<td>London</td>
						<td>41</td>
						<td data-order="1350082800">Sat 13th Oct 12</td>
						<td data-order="132000">$132,000/y</td>
					</tr>
					<tr>
						<td data-search="Dai Rios">D. Rios</td>
						<td>Personnel Lead</td>
						<td>Edinburgh</td>
						<td>35</td>
						<td data-order="1348614000">Wed 26th Sep 12</td>
						<td data-order="217500">$217,500/y</td>
					</tr>
					<tr>
						<td data-search="Jenette Caldwell">J. Caldwell</td>
						<td>Development Lead</td>
						<td>New York</td>
						<td>30</td>
						<td data-order="1315004400">Sat 3rd Sep 11</td>
						<td data-order="345000">$345,000/y</td>
					</tr>
					<tr>
						<td data-search="Yuri Berry">Y. Berry</td>
						<td>Chief Marketing Officer (CMO)</td>
						<td>New York</td>
						<td>40</td>
						<td data-order="1245884400">Thu 25th Jun 09</td>
						<td data-order="675000">$675,000/y</td>
					</tr>
					<tr>
						<td data-search="Caesar Vance">C. Vance</td>
						<td>Pre-Sales Support</td>
						<td>New York</td>
						<td>21</td>
						<td data-order="1323648000">Mon 12th Dec 11</td>
						<td data-order="106450">$106,450/y</td>
					</tr>
					<tr>
						<td data-search="Doris Wilder">D. Wilder</td>
						<td>Sales Assistant</td>
						<td>Sidney</td>
						<td>23</td>
						<td data-order="1284937200">Mon 20th Sep 10</td>
						<td data-order="85600">$85,600/y</td>
					</tr>
					<tr>
						<td data-search="Angelica Ramos">A. Ramos</td>
						<td>Chief Executive Officer (CEO)</td>
						<td>London</td>
						<td>47</td>
						<td data-order="1255042800">Fri 9th Oct 09</td>
						<td data-order="1200000">$1,200,000/y</td>
					</tr>
					<tr>
						<td data-search="Gavin Joyce">G. Joyce</td>
						<td>Developer</td>
						<td>Edinburgh</td>
						<td>42</td>
						<td data-order="1292976000">Wed 22nd Dec 10</td>
						<td data-order="92575">$92,575/y</td>
					</tr>
					<tr>
						<td data-search="Jennifer Chang">J. Chang</td>
						<td>Regional Director</td>
						<td>Singapore</td>
						<td>28</td>
						<td data-order="1289692800">Sun 14th Nov 10</td>
						<td data-order="357650">$357,650/y</td>
					</tr>
					<tr>
						<td data-search="Brenden Wagner">B. Wagner</td>
						<td>Software Engineer</td>
						<td>San Francisco</td>
						<td>28</td>
						<td data-order="1307401200">Tue 7th Jun 11</td>
						<td data-order="206850">$206,850/y</td>
					</tr>
					<tr>
						<td data-search="Fiona Green">F. Green</td>
						<td>Chief Operating Officer (COO)</td>
						<td>San Francisco</td>
						<td>48</td>
						<td data-order="1268265600">Thu 11th Mar 10</td>
						<td data-order="850000">$850,000/y</td>
					</tr>
					<tr>
						<td data-search="Shou Itou">S. Itou</td>
						<td>Regional Marketing</td>
						<td>Tokyo</td>
						<td>20</td>
						<td data-order="1313276400">Sun 14th Aug 11</td>
						<td data-order="163000">$163,000/y</td>
					</tr>
					<tr>
						<td data-search="Michelle House">M. House</td>
						<td>Integration Specialist</td>
						<td>Sidney</td>
						<td>37</td>
						<td data-order="1306969200">Thu 2nd Jun 11</td>
						<td data-order="95400">$95,400/y</td>
					</tr>
					<tr>
						<td data-search="Suki Burks">S. Burks</td>
						<td>Developer</td>
						<td>London</td>
						<td>53</td>
						<td data-order="1256166000">Thu 22nd Oct 09</td>
						<td data-order="114500">$114,500/y</td>
					</tr>
					<tr>
						<td data-search="Prescott Bartlett">P. Bartlett</td>
						<td>Technical Author</td>
						<td>London</td>
						<td>27</td>
						<td data-order="1304722800">Sat 7th May 11</td>
						<td data-order="145000">$145,000/y</td>
					</tr>
					<tr>
						<td data-search="Gavin Cortez">G. Cortez</td>
						<td>Team Leader</td>
						<td>San Francisco</td>
						<td>22</td>
						<td data-order="1224975600">Sun 26th Oct 08</td>
						<td data-order="235500">$235,500/y</td>
					</tr>
					<tr>
						<td data-search="Martena Mccray">M. Mccray</td>
						<td>Post-Sales support</td>
						<td>Edinburgh</td>
						<td>46</td>
						<td data-order="1299628800">Wed 9th Mar 11</td>
						<td data-order="324050">$324,050/y</td>
					</tr>
					<tr>
						<td data-search="Unity Butler">U. Butler</td>
						<td>Marketing Designer</td>
						<td>San Francisco</td>
						<td>47</td>
						<td data-order="1260316800">Wed 9th Dec 09</td>
						<td data-order="85675">$85,675/y</td>
					</tr>
					<tr>
						<td data-search="Howard Hatfield">H. Hatfield</td>
						<td>Office Manager</td>
						<td>San Francisco</td>
						<td>51</td>
						<td data-order="1229385600">Tue 16th Dec 08</td>
						<td data-order="164500">$164,500/y</td>
					</tr>
					<tr>
						<td data-search="Hope Fuentes">H. Fuentes</td>
						<td>Secretary</td>
						<td>San Francisco</td>
						<td>41</td>
						<td data-order="1265932800">Fri 12th Feb 10</td>
						<td data-order="109850">$109,850/y</td>
					</tr>
					<tr>
						<td data-search="Vivian Harrell">V. Harrell</td>
						<td>Financial Controller</td>
						<td>San Francisco</td>
						<td>62</td>
						<td data-order="1234569600">Sat 14th Feb 09</td>
						<td data-order="452500">$452,500/y</td>
					</tr>
					<tr>
						<td data-search="Timothy Mooney">T. Mooney</td>
						<td>Office Manager</td>
						<td>London</td>
						<td>37</td>
						<td data-order="1228953600">Thu 11th Dec 08</td>
						<td data-order="136200">$136,200/y</td>
					</tr>
					<tr>
						<td data-search="Jackson Bradshaw">J. Bradshaw</td>
						<td>Director</td>
						<td>New York</td>
						<td>65</td>
						<td data-order="1222383600">Fri 26th Sep 08</td>
						<td data-order="645750">$645,750/y</td>
					</tr>
					<tr>
						<td data-search="Olivia Liang">O. Liang</td>
						<td>Support Engineer</td>
						<td>Singapore</td>
						<td>64</td>
						<td data-order="1296691200">Thu 3rd Feb 11</td>
						<td data-order="234500">$234,500/y</td>
					</tr>
					<tr>
						<td data-search="Bruno Nash">B. Nash</td>
						<td>Software Engineer</td>
						<td>London</td>
						<td>38</td>
						<td data-order="1304377200">Tue 3rd May 11</td>
						<td data-order="163500">$163,500/y</td>
					</tr>
					<tr>
						<td data-search="Sakura Yamamoto">S. Yamamoto</td>
						<td>Support Engineer</td>
						<td>Tokyo</td>
						<td>37</td>
						<td data-order="1250636400">Wed 19th Aug 09</td>
						<td data-order="139575">$139,575/y</td>
					</tr>
					<tr>
						<td data-search="Thor Walton">T. Walton</td>
						<td>Developer</td>
						<td>New York</td>
						<td>61</td>
						<td data-order="1376175600">Sun 11th Aug 13</td>
						<td data-order="98540">$98,540/y</td>
					</tr>
					<tr>
						<td data-search="Finn Camacho">F. Camacho</td>
						<td>Support Engineer</td>
						<td>San Francisco</td>
						<td>47</td>
						<td data-order="1246921200">Tue 7th Jul 09</td>
						<td data-order="87500">$87,500/y</td>
					</tr>
					<tr>
						<td data-search="Serge Baldwin">S. Baldwin</td>
						<td>Data Coordinator</td>
						<td>Singapore</td>
						<td>64</td>
						<td data-order="1333926000">Mon 9th Apr 12</td>
						<td data-order="138575">$138,575/y</td>
					</tr>
					<tr>
						<td data-search="Zenaida Frank">Z. Frank</td>
						<td>Software Engineer</td>
						<td>New York</td>
						<td>63</td>
						<td data-order="1262563200">Mon 4th Jan 10</td>
						<td data-order="125250">$125,250/y</td>
					</tr>
					<tr>
						<td data-search="Zorita Serrano">Z. Serrano</td>
						<td>Software Engineer</td>
						<td>San Francisco</td>
						<td>56</td>
						<td data-order="1338505200">Fri 1st Jun 12</td>
						<td data-order="115000">$115,000/y</td>
					</tr>
					<tr>
						<td data-search="Jennifer Acosta">J. Acosta</td>
						<td>Junior Javascript Developer</td>
						<td>Edinburgh</td>
						<td>43</td>
						<td data-order="1359676800">Fri 1st Feb 13</td>
						<td data-order="75650">$75,650/y</td>
					</tr>
					<tr>
						<td data-search="Cara Stevens">C. Stevens</td>
						<td>Sales Assistant</td>
						<td>New York</td>
						<td>46</td>
						<td data-order="1323129600">Tue 6th Dec 11</td>
						<td data-order="145600">$145,600/y</td>
					</tr>
					<tr>
						<td data-search="Hermione Butler">H. Butler</td>
						<td>Regional Director</td>
						<td>London</td>
						<td>47</td>
						<td data-order="1300665600">Mon 21st Mar 11</td>
						<td data-order="356250">$356,250/y</td>
					</tr>
					<tr>
						<td data-search="Lael Greer">L. Greer</td>
						<td>Systems Administrator</td>
						<td>London</td>
						<td>21</td>
						<td data-order="1235692800">Fri 27th Feb 09</td>
						<td data-order="103500">$103,500/y</td>
					</tr>
					<tr>
						<td data-search="Jonas Alexander">J. Alexander</td>
						<td>Developer</td>
						<td>San Francisco</td>
						<td>30</td>
						<td data-order="1279062000">Wed 14th Jul 10</td>
						<td data-order="86500">$86,500/y</td>
					</tr>
					<tr>
						<td data-search="Shad Decker">S. Decker</td>
						<td>Regional Director</td>
						<td>Edinburgh</td>
						<td>51</td>
						<td data-order="1226534400">Thu 13th Nov 08</td>
						<td data-order="183000">$183,000/y</td>
					</tr>
					<tr>
						<td data-search="Michael Bruce">M. Bruce</td>
						<td>Javascript Developer</td>
						<td>Singapore</td>
						<td>29</td>
						<td data-order="1309129200">Mon 27th Jun 11</td>
						<td data-order="183000">$183,000/y</td>
					</tr>
					<tr>
						<td data-search="Donna Snider">D. Snider</td>
						<td>Customer Support</td>
						<td>New York</td>
						<td>27</td>
						<td data-order="1295913600">Tue 25th Jan 11</td>
						<td data-order="112000">$112,000/y</td>
					</tr>
				</tbody>
			</table>

			<ul class="tabs">
				<li class="active">Javascript</li>
				<li>HTML</li>
				<li>CSS</li>
				<li>Ajax</li>
				<li>Server-side script</li>
			</ul>

			<div class="tabs">
				<div class="js">
					<p>The Javascript shown below is used to initialise the table shown in this
					example:</p><code class="multiline brush: js;">$(document).ready(function() {
	$('#example').dataTable();
} );</code>

					<p>In addition to the above code, the following Javascript library files are loaded for use in this
					example:</p>

					<ul>
						<li><a href="../../media/js/jquery.js">../../media/js/jquery.js</a></li>
						<li><a href="../../media/js/jquery.dataTables.js">../../media/js/jquery.dataTables.js</a></li>
					</ul>
				</div>

				<div class="table">
					<p>The HTML shown below is the raw HTML table element, before it has been enhanced by
					DataTables:</p>
				</div>

				<div class="css">
					<div>
						<p>This example uses a little bit of additional CSS beyond what is loaded from the library
						files (below), in order to correctly display the table. The additional CSS used is shown
						below:</p><code class="multiline brush: js;"></code>
					</div>

					<p>The following CSS library files are loaded for use in this example to provide the styling of the
					table:</p>

					<ul>
						<li><a href=
						"../../media/css/jquery.dataTables.css">../../media/css/jquery.dataTables.css</a></li>
					</ul>
				</div>

				<div class="ajax">
					<p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data
					will update automatically as any additional data is loaded.</p>
				</div>

				<div class="php">
					<p>The script used to perform the server-side processing for this table is shown below. Please note
					that this is just an example script using PHP. Server-side processing scripts can be written in any
					language, using <a href="//datatables.net/manual/server-side">the protocol described in the
					DataTables documentation</a>.</p>
				</div>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<h2>Other examples</h2>

				<div class="toc">
					<div class="toc-group">
						<h3><a href="../basic_init/index.html">Basic initialisation</a></h3>
						<ul class="toc">
							<li><a href="../basic_init/zero_configuration.html">Zero configuration</a></li>
							<li><a href="../basic_init/filter_only.html">Feature enable / disable</a></li>
							<li><a href="../basic_init/table_sorting.html">Default ordering (sorting)</a></li>
							<li><a href="../basic_init/multi_col_sort.html">Multi-column ordering</a></li>
							<li><a href="../basic_init/multiple_tables.html">Multiple tables</a></li>
							<li><a href="../basic_init/hidden_columns.html">Hidden columns</a></li>
							<li><a href="../basic_init/complex_header.html">Complex headers (rowspan and
							colspan)</a></li>
							<li><a href="../basic_init/dom.html">DOM positioning</a></li>
							<li><a href="../basic_init/flexible_width.html">Flexible table width</a></li>
							<li><a href="../basic_init/state_save.html">State saving</a></li>
							<li><a href="../basic_init/alt_pagination.html">Alternative pagination</a></li>
							<li><a href="../basic_init/scroll_y.html">Scroll - vertical</a></li>
							<li><a href="../basic_init/scroll_x.html">Scroll - horizontal</a></li>
							<li><a href="../basic_init/scroll_xy.html">Scroll - horizontal and vertical</a></li>
							<li><a href="../basic_init/scroll_y_theme.html">Scroll - vertical with jQuery UI
							ThemeRoller</a></li>
							<li><a href="../basic_init/comma-decimal.html">Language - Comma decimal place</a></li>
							<li><a href="../basic_init/language.html">Language options</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./index.html">Advanced initialisation</a></h3>
						<ul class="toc active">
							<li><a href="./events_live.html">DOM / jQuery events</a></li>
							<li><a href="./dt_events.html">DataTables events</a></li>
							<li><a href="./column_render.html">Column rendering</a></li>
							<li><a href="./length_menu.html">Page length options</a></li>
							<li><a href="./dom_multiple_elements.html">Multiple table control elements</a></li>
							<li><a href="./complex_header.html">Complex headers (rowspan / colspan)</a></li>
							<li><a href="./object_dom_read.html">Read HTML to data objects</a></li>
							<li class="active"><a href="./html5-data-attributes.html">HTML5 data-* attributes</a></li>
							<li><a href="./language_file.html">Language file</a></li>
							<li><a href="./defaults.html">Setting defaults</a></li>
							<li><a href="./row_callback.html">Row created callback</a></li>
							<li><a href="./row_grouping.html">Row grouping</a></li>
							<li><a href="./footer_callback.html">Footer callback</a></li>
							<li><a href="./dom_toolbar.html">Custom toolbar elements</a></li>
							<li><a href="./sort_direction_control.html">Order direction sequence control</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="../styling/display.html">Base style</a></li>
							<li><a href="../styling/no-classes.html">Base style - no styling classes</a></li>
							<li><a href="../styling/cell-border.html">Base style - cell borders</a></li>
							<li><a href="../styling/compact.html">Base style - compact</a></li>
							<li><a href="../styling/hover.html">Base style - hover</a></li>
							<li><a href="../styling/order-column.html">Base style - order-column</a></li>
							<li><a href="../styling/row-border.html">Base style - row borders</a></li>
							<li><a href="../styling/stripe.html">Base style - stripe</a></li>
							<li><a href="../styling/bootstrap.html">Bootstrap</a></li>
							<li><a href="../styling/foundation.html">Foundation</a></li>
							<li><a href="../styling/jqueryUI.html">jQuery UI ThemeRoller</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../data_sources/index.html">Data sources</a></h3>
						<ul class="toc">
							<li><a href="../data_sources/dom.html">HTML (DOM) sourced data</a></li>
							<li><a href="../data_sources/ajax.html">Ajax sourced data</a></li>
							<li><a href="../data_sources/js_array.html">Javascript sourced data</a></li>
							<li><a href="../data_sources/server_side.html">Server-side processing</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../api/index.html">API</a></h3>
						<ul class="toc">
							<li><a href="../api/add_row.html">Add rows</a></li>
							<li><a href="../api/multi_filter.html">Individual column searching (text inputs)</a></li>
							<li><a href="../api/multi_filter_select.html">Individual column searching (select
							inputs)</a></li>
							<li><a href="../api/highlight.html">Highlighting rows and columns</a></li>
							<li><a href="../api/row_details.html">Child rows (show extra / detailed
							information)</a></li>
							<li><a href="../api/select_row.html">Row selection (multiple rows)</a></li>
							<li><a href="../api/select_single_row.html">Row selection and deletion (single
							row)</a></li>
							<li><a href="../api/form.html">Form inputs</a></li>
							<li><a href="../api/counter_columns.html">Index column</a></li>
							<li><a href="../api/show_hide.html">Show / hide columns dynamically</a></li>
							<li><a href="../api/api_in_init.html">Using API in callbacks</a></li>
							<li><a href="../api/tabs_and_scrolling.html">Scrolling and jQuery UI tabs</a></li>
							<li><a href="../api/regex.html">Search API (regular expressions)</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../ajax/index.html">Ajax</a></h3>
						<ul class="toc">
							<li><a href="../ajax/simple.html">Ajax data source (arrays)</a></li>
							<li><a href="../ajax/objects.html">Ajax data source (objects)</a></li>
							<li><a href="../ajax/deep.html">Nested object data (objects)</a></li>
							<li><a href="../ajax/objects_subarrays.html">Nested object data (arrays)</a></li>
							<li><a href="../ajax/orthogonal-data.html">Orthogonal data</a></li>
							<li><a href="../ajax/null_data_source.html">Generated content for a column</a></li>
							<li><a href="../ajax/custom_data_property.html">Custom data source property</a></li>
							<li><a href="../ajax/custom_data_flat.html">Flat array data source</a></li>
							<li><a href="../ajax/defer_render.html">Deferred rendering for speed</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../server_side/index.html">Server-side</a></h3>
						<ul class="toc">
							<li><a href="../server_side/simple.html">Server-side processing</a></li>
							<li><a href="../server_side/custom_vars.html">Custom HTTP variables</a></li>
							<li><a href="../server_side/post.html">POST data</a></li>
							<li><a href="../server_side/ids.html">Automatic addition of row ID attributes</a></li>
							<li><a href="../server_side/object_data.html">Object data source</a></li>
							<li><a href="../server_side/row_details.html">Row details</a></li>
							<li><a href="../server_side/select_rows.html">Row selection</a></li>
							<li><a href="../server_side/jsonp.html">JSONP data source for remote domains</a></li>
							<li><a href="../server_side/defer_loading.html">Deferred loading of data</a></li>
							<li><a href="../server_side/pipeline.html">Pipelining data to reduce Ajax calls for
							paging</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../plug-ins/index.html">Plug-ins</a></h3>
						<ul class="toc">
							<li><a href="../plug-ins/api.html">API plug-in methods</a></li>
							<li><a href="../plug-ins/sorting_auto.html">Ordering plug-ins (with type
							detection)</a></li>
							<li><a href="../plug-ins/sorting_manual.html">Ordering plug-ins (no type
							detection)</a></li>
							<li><a href="../plug-ins/range_filtering.html">Custom filtering - range search</a></li>
							<li><a href="../plug-ins/dom_sort.html">Live DOM ordering</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>