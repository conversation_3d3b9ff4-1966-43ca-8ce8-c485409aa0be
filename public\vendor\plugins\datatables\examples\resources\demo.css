
@font-face {
	/* <PERSON><PERSON><PERSON>ay Thin from - https://www.theleagueofmoveabletype.com, Font Squirrel for Web Font creation with "Adjust Glyph Spacing" -50 */
    font-family: 'ralewaythin';
    src: url('font/raleway_thin-webfont.eot');
    src: url('font/raleway_thin-webfont.eot?#iefix') format('embedded-opentype'),
         url('font/raleway_thin-webfont.woff') format('woff'),
         url('font/raleway_thin-webfont.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}


body {
	font: 90%/1.45em "Helvetica Neue", HelveticaNeue, Verdana, Arial, Helvetica, sans-serif;
	margin: 0;
	padding: 0;
	color: #333;
	background-color: #fff;
}


div.container {
	max-width: 980px;
	margin: 0 auto;
}

h1 {
	font-family: 'HelveticaNeue-UltraLight', 'Helvetica Neue UltraLight', 'ralewaythin', 'Helvetica Neue', Arial, Helvetica, sans-serif;
    font-weight: 100;
    letter-spacing: 1px;
	font-size: 3em;
	line-height: 1em;
}

h1 span {
	font-size: 0.5em;
	line-height: 1em;
}

a {
	cursor: pointer;
	color: #3174c7;
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

div.toc ul {
	color: #4E6CA3;
	list-style-type: none;
	padding-left: 0;
}

div.toc li {
	padding: 0.2em 1em;
	border-left: 4px solid transparent;
	border-bottom: 1px solid #e6e6e6;
}

div.toc li.active {
	border-left: 4px solid #458ae0;
}


div.toc li:first-child {
	border-top: 1px solid #efefef;
}

div.toc li:last-child {
	border-bottom: 1px solid #efefef;
}


div.epilogue {
	text-align: center;
}

p.copyright {
	font-size: 0.8em;
	padding-bottom: 2em;
	margin-bottom: 0;
}

.clear {
	clear: both;
	height: 0;
}


div.info {
	margin-bottom: 2em;

	-webkit-column-count: 2;
	   -moz-column-count: 2;
	    -ms-column-count: 2;
	     -o-column-count: 2;
	column-count: 2;


	-webkit-column-rule: 1px solid #F3F3F3;
	   -moz-column-rule: 1px solid #F3F3F3;
	    -ms-column-rule: 1px solid #F3F3F3;
	     -o-column-rule: 1px solid #F3F3F3;
	column-rule: 1px solid #F3F3F3;
}

div.info > * {
	-webkit-column-break-inside: avoid;
	break-inside: avoid;
}

div.info li {
	margin-top: 0.75em;
}

div.info p:first-child {
	margin-top: 0;
}

div.footer {
	position: relative;
	margin-top: 3em;
	border-top: 1px solid #999;
	background-color: #eee;
}

div.footer > div.liner {
	max-width: 960px;
	margin: 0 auto;
}

div.footer > div.gradient {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 6px;

	background: -moz-linear-gradient(top, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0) 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.2)), color-stop(100%,rgba(0,0,0,0))); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, rgba(0,0,0,0.2) 0%,rgba(0,0,0,0) 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, rgba(0,0,0,0.2) 0%,rgba(0,0,0,0) 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, rgba(0,0,0,0.2) 0%,rgba(0,0,0,0) 100%); /* IE10+ */
	background: linear-gradient(to bottom, rgba(0,0,0,0.2) 0%,rgba(0,0,0,0) 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a6000000', endColorstr='#00000000',GradientType=0 ); /* IE6-9 */
}

div.toc {
	-webkit-column-count: 2;
	   -moz-column-count: 2;
	    -ms-column-count: 2;
	     -o-column-count: 2;
	column-count: 2;
}

div.toc-group {
	display: inline-block;
	width: 100%;
}

div.box {
	overflow: auto;
	height: 8em;
	padding: 1em;
	color: #444;
	background-color: #fcfcfc;
	border: 1px solid #e0e0e0;
	margin-bottom: 2em;
}


code {
	font-family: "Source Code Pro", Consolas, Menlo, Monaco, "Courier New", monospace;
	padding: 1px 4px;
	font-size: 0.8em;

	color: #444;
	background-color: #fcfcfc;

	border: 1px solid #e0e0e0;
	-webkit-border-radius: 3px;
	   -moz-border-radius: 3px;
	        border-radius: 3px;
}

code > span {
	border-left: 1px solid rgba( 0, 0, 0, 0.2 );
	margin-left: 4px;
	padding-left: 4px;
	opacity: 0.5;
}

code.option {
	color: #D14; /* red */
	background-color: #fcf6f8;
	border: 1px solid #f7d6df;
}

code.path {
	color: #095c05; /* dark green */
	border: 1px solid #D6E9C6;
}

code.tag {
	color: #a1a713; /* yellow */
	background-color: #f7f8e6;
	border: 1px solid #D6E9C6;
}

code.api {
	color: #0c199c; /* dark blue */
	background-color: #f4f5fc;
	border: 1px solid #c6cbe9;
}

code.type {
	color: #d119cf; /* purple */
	background-color: #faebfa;
	border: 1px solid #f3aef2;
}

code.event {
	color: #2a839e; /* deep aqua */
	background-color: #f5fafb;
	border: 1px solid #a8ddec;
}

code.string {
	color: #e8941e; /* orange */
	background-color: #fcf8f1;
	border: 1px solid #f7e4c9;
}

code.field {
	color: #ad1ee8; /* purple */
	background-color: #f9f1fc;
	border: 1px solid #ebc9f7;
}

code.multiline {
	display: inline-block;
	width: 95%;
}


ul.tabs {
	position: relative;
	top: 1px;
	height: 40px;
	margin: 20px 20px 0 0;
}


ul.tabs li {
	display: block;
	float: left;
	padding: 0 15px;
	height: 40px;
	font-size: 1.2em;
	margin: 0 5px;
	cursor: pointer;
	line-height: 40px;
	color: #121e32;
	border: 1px solid white;
	border-bottom: none;
	margin-top: -1px;
}

ul.tabs li.active {
	border: 1px solid #ccc;
	border-bottom: 1px solid white;
	margin-top: 0;
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
}

ul.tabs li:hover {
	background-color: #fafafa;
}

ul.tabs li.active:hover {
	background-color: white;
}

div.tabs {
	clear: both;
}

div.tabs>div {
	padding: 0 15px;
	border: 1px solid #ccc;
	margin-top: 1px;
	display: none;
	border-radius: 5px;
	box-shadow: 2px 2px 2px #bbb;
}

div.tabs>div h1 {
	border-bottom: none;
	margin-top: 1em;
}

div.column_half {
	float: left;
	width: 49%;
	padding-right: 1%;
}


@media only screen and (max-width : 979px) {
	div.container,
	div.footer {
		padding: 0 1em;
	}
}

@media
	screen and (max-width : 767px),
	screen and (max-width : 768px) and (orientation: portrait) {
	div.info {
		-webkit-column-count: 1;
		   -moz-column-count: 1;
		    -ms-column-count: 1;
		     -o-column-count: 1;
		column-count: 1;
	}

	div.toc {
		-webkit-column-count: 1;
		   -moz-column-count: 1;
		    -ms-column-count: 1;
		     -o-column-count: 1;
		column-count: 1;
	}

	h1 span {
		display: block;
	}
}

