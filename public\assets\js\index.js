var $TABLE = $('#table');
var $BTN = $('#export-btn');
var $EXPORT = $('#export');

function drawTable(data) {
    for (var i = 0; i < data.length; i++) {
        drawRow(data[i],i);
    }
}

function drawRow(rowData,i) {
    var invoice_status = rowData.ticket_invoice_status;
    var invoice_status_text = '';
    if( invoice_status > 0 ) {
        invoice_status_text = "checked";
    }

    var row = $("<tr />");
    $("#personDataTable").append(row); //this will append tr element to table... keep its reference for a while since we will add cels into it
    row.append($('<td style="border: solid 1px #efefeb;"><select name="expense_type" class="form-control" style="width: auto;" id='+rowData.id+'>' +
        ' <option value="0">Uçak Bileti</option>\n' +
        ' <option value="1">Toplantı Odası Rez.</option>\n' +
        ' <option value="2">Müşteri konaklama</option>\n' +
        ' <option value="3">Müşteri uçak bileti</option>\n' +
        ' <option value="4">Ekip konaklama ücreti</option>\n' +
        ' <option value="5">Taksi (Ulaşım)</option>\n' +
        ' <option value="7">Çeviri giderleri</option>\n'+
        ' <option value="6">Diğer</option>'));
    $("#"+rowData.id).val(rowData.expense_type);
    row.append($('<td contenteditable="true"  style="border: solid 1px #efefeb; word-break: break-all;">'+rowData.name+' <em for="newname" class="field prepend-icon state-error" style="color: red;"></em></td>'));
    row.append($('<td contenteditable="true"  style="border: solid 1px #efefeb;">'+rowData.ticket_company+' <em for="newticket_company" class="field prepend-icon state-error" style="color: red;"></em></td>'));
    row.append($('<td style="border: solid 1px #efefeb;"><label><input type="checkbox" id="checkboxDefault5" '+ invoice_status_text +' name="ticket_invoice_status" value="1"> Fatura Geldi</label></td>'));
    row.append($('<td contenteditable="true" class="sayikontrol2" style="border: solid 1px #efefeb;">'+rowData.amount+' <em for="newamount" class="field prepend-icon state-error" style="color: red;"></em></td>'));
    row.append($('<td  style="border: solid 1px #efefeb;"><select class="form-control" style="width: fit-content;" id='+rowData.currency+'>' +
        '<option value="TL">TL</option><option value="USD">USD</option><option value="EU">EU</option><option value="GBP">GBP</option></select>'));
    $("#"+rowData.currency).val(rowData.currency);


    row.append($('<td style="border: solid 1px #efefeb;" ><span class="table-remove fa fa-trash fa-2x"  style="color: red;"></span></td></tr></table>'));

    row.find('.table-remove').click(function () {
        var r = confirm("Bu masrafı silmek istediğinizden emin misiniz?");
        if (r == true) {
            $(this).parents('tr').detach();
        } else {
        }
    });

    row.find('.table-up').click(function () {
        var $row = $(this).parents('tr');
        if ($row.index() === 1) return; // Don't go above the header
        $row.prev().before($row.get(0));
    });
    row.find('.table-down').click(function () {
        var $row = $(this).parents('tr');
        $row.next().after($row.get(0));
    });

    $(document).ready(function(){
        $(".sayikontrol2").on("keypress", function(event){
            if(   (event.keyCode != 44   && event.keyCode < 48 || event.keyCode > 57))
                return false;
        });

        $("#sayisal_form").on("keyup", function(event){
            form_kontrolu();
        });
    });

}

$('.table-add').click(function () {
    var $clone = $TABLE.find('tr.hide').clone(true).removeClass('hide table-line');
    $clone.find("input.datepicker").each(function(){
        $(this).attr("id", "").removeData().off();
        $(this).find('.add-on').removeData().off();
        $(this).find('input').removeData().off();
        $(this).timepicker({defaultTime:'16:20', minuteStep: 1, showMeridian: false});
    });
    $TABLE.find('table').append($clone).find("input.datepicker").addClass('datepicker');
});

$('.table-remove').click(function () {
    $(this).parents('tr').detach();
});

$('.table-up').click(function () {
    var $row = $(this).parents('tr');
    if ($row.index() === 1) return; // Don't go above the header
    $row.prev().before($row.get(0));
});

$('.table-down').click(function () {
    var $row = $(this).parents('tr');
    $row.next().after($row.get(0));
});


// A few jQuery helpers for exporting only
jQuery.fn.pop = [].pop;
jQuery.fn.shift = [].shift;

$BTN.click(function () {
  var $rows = $TABLE.find('tr:not(:hidden)');
  var $tds = $TABLE.find('td:not(:hidden)');
  var headers = [];
  var data = [];
  var cevir=['expense_type','name','ticket_company','ticket_invoice_status','amount','currency'];

  // Get the headers (add special header logic here)
  $($rows.shift()).find('th:not(:empty)').each(function () {
    headers.push($(this).text().toLowerCase().replace(' ',''));
  });

    headers[0]=headers[0].replace(headers[0],cevir[0]);
    headers[1]=headers[1].replace(headers[1],cevir[1]);
    headers[2]=headers[2].replace(headers[2],cevir[2]);
    headers[3]=headers[3].replace(headers[3],cevir[3]);
    headers[4]=headers[4].replace(headers[4],cevir[4]);
    headers[5]=headers[5].replace(headers[5],cevir[5]);

  // Turn all existing rows into a loopable array
  $rows.each(function () {
    var $td = $(this).find('td');
    var $td2 = $td.find('input');
    var $td3 = $td.find('select');
    var h2 = {};

    // Use the headers from earlier to name our hash keys
    headers.forEach(function (header, i) {
        var row_input = $td.eq(i).find('input,select');
        h2[header] = row_input.val() || $td.eq(i).text();
        h2[header] = $.trim(h2[header]);
        if (row_input.attr('type') == 'checkbox' && ! row_input.is(':checked')) {
            //
            delete h2[header];
        }
    });

    data.push(h2);
  });

  // Output the result
  $EXPORT.text(JSON.stringify(data));
});
