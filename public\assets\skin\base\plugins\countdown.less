/* ==================================================
  Countdown
 ==================================================== */
 
#counter {
    width: 100%;
    min-height: 200px;
    padding: 30px 20px;
    margin-bottom: 100px;
}
.is-countdown {
}
.countdown-rtl {
    direction: rtl;
}
.countdown-holding span {
    color: #888;
}
.countdown-row {
    clear: both;
    width: 100%;
    padding: 0px 2px;
    text-align: center;
}
.countdown-show1 .countdown-section {
    width: 98%;
}
.countdown-show2 .countdown-section {
    width: 48%;
}
.countdown-show3 .countdown-section {
    width: 32.5%;
}
.countdown-show4 .countdown-section {
    width: 24.5%;
}
.countdown-show5 .countdown-section {
    width: 19.5%;
}
.countdown-show6 .countdown-section {
    width: 16.25%;
}
.countdown-show7 .countdown-section {
    width: 14%;
}
.countdown-section {
    position: relative;
    display: block;
    float: left;
    font-size: 75%;
    text-align: center;
}
.countdown-section:after {
    content: "";
    position: absolute;
    width: 5px;
    height: 45%;
    margin-top: 18%;
    top: 0;
    right: 0;
    background: rgba(255,255,255, 0.5);
}
// .countdown-section:after {
//  content: "";
//  position: absolute;
//  width: 25px;
//  height: 5px;
//  margin-top: 45%;
//  top: 0;
//  right: -8px;
//  background: rgba(255,255,255, 0.5);
// }
.countdown-section:last-child:after {
    display: none;
}
.countdown-amount {
    color: #FFF;
    font-weight: 200;
    font-size: 90px;
}
.countdown-period {
    display: block;
    color: #FFF;
    font-weight: 200;
    font-size: 24px;
}
.countdown-descr {
    display: block;
    width: 100%;
}