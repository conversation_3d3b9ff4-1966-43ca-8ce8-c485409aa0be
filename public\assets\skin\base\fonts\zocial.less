@charset "UTF-8";

/*!
	Zocial Butons
	http://zocial.smcllns.com
	by <PERSON> (@smcllns)
	License: http://opensource.org/licenses/mit-license.php
	
	You are free to use and modify, as long as you keep this license comment intact or link back to zocial.smcllns.com on your site.
*/


/* Button structure */

.zocial,
a.zocial {
	border: 1px solid #777;
	border-color: rgba(0,0,0,0.2);
	border-bottom-color: #333;
	border-bottom-color: rgba(0,0,0,0.4);
	color: #fff;
	-moz-box-shadow: inset 0 0.08em 0 rgba(255,255,255,0.4), inset 0 0 0.1em rgba(255,255,255,0.9);
	-webkit-box-shadow: inset 0 0.08em 0 rgba(255,255,255,0.4), inset 0 0 0.1em rgba(255,255,255,0.9);
	box-shadow: inset 0 0.08em 0 rgba(255,255,255,0.4), inset 0 0 0.1em rgba(255,255,255,0.9);
	cursor: pointer;
	display: inline-block;
	font: bold 100%/2.1 "Lucida Grande", Tahoma, sans-serif;
	padding: 0 .95em 0 0;
	text-align: center;
	text-decoration: none;
	text-shadow: 0 1px 0 rgba(0,0,0,0.5);
	white-space: nowrap;
	
	-moz-user-select: none;
	-webkit-user-select: none;
	user-select: none;
	
	position: relative;
	
	-moz-border-radius: .3em;
	-webkit-border-radius: .3em;
	border-radius: .3em;
}

.zocial:before {
	content: "";
	border-right: 0.075em solid rgba(0,0,0,0.1);
	float: left;
	font: 120%/1.65 zocial;
	font-style: normal;
	font-weight: normal;
	margin: 0 0.5em 0 0;
	padding: 0 0.5em;
	text-align: center;
	text-decoration: none;
	text-transform: none;
	
	-moz-box-shadow: 0.075em 0 0 rgba(255,255,255,0.25);
	-webkit-box-shadow: 0.075em 0 0 rgba(255,255,255,0.25);
	box-shadow: 0.075em 0 0 rgba(255,255,255,0.25);
	
	-moz-font-smoothing: antialiased;
	-webkit-font-smoothing: antialiased;
	font-smoothing: antialiased;
}

.zocial:active {
	outline: none; /* outline is visible on :focus */
}

/* Buttons can be displayed as standalone icons by adding a class of "icon" */

.zocial.icon {
	overflow: hidden;
	max-width: 2.4em;
	padding-left: 0;
	padding-right: 0;
	max-height: 2.15em;
	white-space: nowrap;
}
.zocial.icon:before {
	padding: 0;
	width: 2em;
	height: 2em;
	
	box-shadow: none;
	border: none;
}

/* Gradients */

.zocial {
	background-image: -moz-linear-gradient(rgba(255,255,255,.1), rgba(255,255,255,.05) 49%, rgba(0,0,0,.05) 51%, rgba(0,0,0,.1));
	background-image: -ms-linear-gradient(rgba(255,255,255,.1), rgba(255,255,255,.05) 49%, rgba(0,0,0,.05) 51%, rgba(0,0,0,.1));
	background-image: -o-linear-gradient(rgba(255,255,255,.1), rgba(255,255,255,.05) 49%, rgba(0,0,0,.05) 51%, rgba(0,0,0,.1));
	background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,.1)), color-stop(49%, rgba(255,255,255,.05)), color-stop(51%, rgba(0,0,0,.05)), to(rgba(0,0,0,.1)));
	background-image: -webkit-linear-gradient(rgba(255,255,255,.1), rgba(255,255,255,.05) 49%, rgba(0,0,0,.05) 51%, rgba(0,0,0,.1));
	background-image: linear-gradient(rgba(255,255,255,.1), rgba(255,255,255,.05) 49%, rgba(0,0,0,.05) 51%, rgba(0,0,0,.1));
}

.zocial:hover, .zocial:focus {
	background-image: -moz-linear-gradient(rgba(255,255,255,.15) 49%, rgba(0,0,0,.1) 51%, rgba(0,0,0,.15));
	background-image: -ms-linear-gradient(rgba(255,255,255,.15) 49%, rgba(0,0,0,.1) 51%, rgba(0,0,0,.15));
	background-image: -o-linear-gradient(rgba(255,255,255,.15) 49%, rgba(0,0,0,.1) 51%, rgba(0,0,0,.15));
	background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,.15)), color-stop(49%, rgba(255,255,255,.15)), color-stop(51%, rgba(0,0,0,.1)), to(rgba(0,0,0,.15)));
	background-image: -webkit-linear-gradient(rgba(255,255,255,.15) 49%, rgba(0,0,0,.1) 51%, rgba(0,0,0,.15));
	background-image: linear-gradient(rgba(255,255,255,.15) 49%, rgba(0,0,0,.1) 51%, rgba(0,0,0,.15));
}

.zocial:active {
	background-image: -moz-linear-gradient(bottom, rgba(255,255,255,.1), rgba(255,255,255,0) 30%, transparent 50%, rgba(0,0,0,.1));
	background-image: -ms-linear-gradient(bottom, rgba(255,255,255,.1), rgba(255,255,255,0) 30%, transparent 50%, rgba(0,0,0,.1));
	background-image: -o-linear-gradient(bottom, rgba(255,255,255,.1), rgba(255,255,255,0) 30%, transparent 50%, rgba(0,0,0,.1));
	background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,.1)), color-stop(30%, rgba(255,255,255,0)), color-stop(50%, transparent), to(rgba(0,0,0,.1)));
	background-image: -webkit-linear-gradient(bottom, rgba(255,255,255,.1), rgba(255,255,255,0) 30%, transparent 50%, rgba(0,0,0,.1));
	background-image: linear-gradient(bottom, rgba(255,255,255,.1), rgba(255,255,255,0) 30%, transparent 50%, rgba(0,0,0,.1));
}

/* Adjustments for light background buttons */

.zocial.acrobat,
.zocial.bitcoin,
.zocial.cloudapp,
.zocial.dropbox,
.zocial.email,
.zocial.eventful,
.zocial.github,
.zocial.gmail,
.zocial.instapaper,
.zocial.itunes,
.zocial.ninetyninedesigns,
.zocial.openid,
.zocial.plancast,
.zocial.pocket,
.zocial.posterous,
.zocial.reddit,
.zocial.secondary,
.zocial.stackoverflow,
.zocial.viadeo,
.zocial.weibo,
.zocial.wikipedia {
	border: 1px solid #aaa;
	border-color: rgba(0,0,0,0.3);
	border-bottom-color: #777;
	border-bottom-color: rgba(0,0,0,0.5);
	-moz-box-shadow: inset 0 0.08em 0 rgba(255,255,255,0.7), inset 0 0 0.08em rgba(255,255,255,0.5);
	-webkit-box-shadow: inset 0 0.08em 0 rgba(255,255,255,0.7), inset 0 0 0.08em rgba(255,255,255,0.5);
	box-shadow: inset 0 0.08em 0 rgba(255,255,255,0.7), inset 0 0 0.08em rgba(255,255,255,0.5);
	text-shadow: 0 1px 0 rgba(255,255,255,0.8);
}

/* :hover adjustments for light background buttons */

.zocial.acrobat:focus,
.zocial.acrobat:hover,
.zocial.bitcoin:focus,
.zocial.bitcoin:hover,
.zocial.dropbox:focus,
.zocial.dropbox:hover,
.zocial.email:focus,
.zocial.email:hover,
.zocial.eventful:focus,
.zocial.eventful:hover,
.zocial.github:focus,
.zocial.github:hover,
.zocial.gmail:focus,
.zocial.gmail:hover,
.zocial.instapaper:focus,
.zocial.instapaper:hover,
.zocial.itunes:focus,
.zocial.itunes:hover,
.zocial.ninetyninedesigns:focus,
.zocial.ninetyninedesigns:hover,
.zocial.openid:focus,
.zocial.openid:hover,
.zocial.plancast:focus,
.zocial.plancast:hover,
.zocial.pocket:focus,
.zocial.pocket:hover,
.zocial.posterous:focus,
.zocial.posterous:hover,
.zocial.reddit:focus,
.zocial.reddit:hover,
.zocial.secondary:focus,
.zocial.secondary:hover,
.zocial.stackoverflow:focus,
.zocial.stackoverflow:hover,
.zocial.twitter:focus,
.zocial.viadeo:focus,
.zocial.viadeo:hover,
.zocial.weibo:focus,
.zocial.weibo:hover,
.zocial.wikipedia:focus,
.zocial.wikipedia:hover {
	background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0.5)), color-stop(49%, rgba(255,255,255,0.2)), color-stop(51%, rgba(0,0,0,0.05)), to(rgba(0,0,0,0.15)));
	background-image: -moz-linear-gradient(top, rgba(255,255,255,0.5), rgba(255,255,255,0.2) 49%, rgba(0,0,0,0.05) 51%, rgba(0,0,0,0.15));
	background-image: -webkit-linear-gradient(top, rgba(255,255,255,0.5), rgba(255,255,255,0.2) 49%, rgba(0,0,0,0.05) 51%, rgba(0,0,0,0.15));
	background-image: -o-linear-gradient(top, rgba(255,255,255,0.5), rgba(255,255,255,0.2) 49%, rgba(0,0,0,0.05) 51%, rgba(0,0,0,0.15));
	background-image: -ms-linear-gradient(top, rgba(255,255,255,0.5), rgba(255,255,255,0.2) 49%, rgba(0,0,0,0.05) 51%, rgba(0,0,0,0.15));
	background-image: linear-gradient(top, rgba(255,255,255,0.5), rgba(255,255,255,0.2) 49%, rgba(0,0,0,0.05) 51%, rgba(0,0,0,0.15));
}

/* :active adjustments for light background buttons */

.zocial.acrobat:active,
.zocial.bitcoin:active,
.zocial.dropbox:active,
.zocial.email:active,
.zocial.eventful:active,
.zocial.github:active,
.zocial.gmail:active,
.zocial.instapaper:active,
.zocial.itunes:active,
.zocial.ninetyninedesigns:active,
.zocial.openid:active,
.zocial.plancast:active,
.zocial.pocket:active,
.zocial.posterous:active,
.zocial.reddit:active,
.zocial.secondary:active,
.zocial.stackoverflow:active,
.zocial.viadeo:active,
.zocial.weibo:active,
.zocial.wikipedia:active {
	background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255,255,255,0)), color-stop(30%, rgba(255,255,255,0)), color-stop(50%, rgba(0,0,0,0)), to(rgba(0,0,0,0.1)));
	background-image: -moz-linear-gradient(bottom, rgba(255,255,255,0), rgba(255,255,255,0) 30%, rgba(0,0,0,0) 50%, rgba(0,0,0,0.1));
	background-image: -webkit-linear-gradient(bottom, rgba(255,255,255,0), rgba(255,255,255,0) 30%, rgba(0,0,0,0) 50%, rgba(0,0,0,0.1));
	background-image: -o-linear-gradient(bottom, rgba(255,255,255,0), rgba(255,255,255,0) 30%, rgba(0,0,0,0) 50%, rgba(0,0,0,0.1));
	background-image: -ms-linear-gradient(bottom, rgba(255,255,255,0), rgba(255,255,255,0) 30%, rgba(0,0,0,0) 50%, rgba(0,0,0,0.1));
	background-image: linear-gradient(bottom, rgba(255,255,255,0), rgba(255,255,255,0) 30%, rgba(0,0,0,0) 50%, rgba(0,0,0,0.1));
}

/* Button icon and color */
/* Icon characters are stored in unicode private area */
.zocial.acrobat:before {content: "\00E3"; color: #FB0000;}
.zocial.amazon:before {content: "a";}
.zocial.android:before {content: "&";}
.zocial.angellist:before {content: "\00D6";}
.zocial.aol:before {content: "\"";}
.zocial.appnet:before {content: "\00E1";}
.zocial.appstore:before {content: "A";}
.zocial.bitbucket:before {content: "\00E9";}
.zocial.bitcoin:before {content: "2"; color: #f7931a;}
.zocial.blogger:before {content: "B";}
.zocial.buffer:before {content: "\00E5";}
.zocial.call:before {content: "7";}
.zocial.cal:before {content: ".";}
.zocial.cart:before {content: "\00C9";}
.zocial.chrome:before {content: "[";}
.zocial.cloudapp:before {content: "c";}
.zocial.creativecommons:before {content: "C";}
.zocial.delicious:before {content: "#";}
.zocial.digg:before {content: ";";}
.zocial.disqus:before {content: "Q";}
.zocial.dribbble:before {content: "D";}
.zocial.dropbox:before {content: "d"; color: #1f75cc;}
.zocial.drupal:before {content: "\00E4"; color: #fff;}
.zocial.dwolla:before {content: "\00E0";}
.zocial.email:before {content: "]"; color: #312c2a;}
.zocial.eventasaurus:before {content: "v"; color: #9de428;}
.zocial.eventbrite:before {content: "|";}
.zocial.eventful:before {content: "'"; color: #0066CC;}
.zocial.evernote:before {content: "E";}
.zocial.facebook:before {content: "f";}
.zocial.fivehundredpx:before {content: "0"; color: #29b6ff;}
.zocial.flattr:before {content: "%";}
.zocial.flickr:before {content: "F";}
.zocial.forrst:before {content: ":"; color: #50894f;}
.zocial.foursquare:before {content: "4";}
.zocial.github:before {content: "\00E8";}
.zocial.gmail:before {content: "m"; color: #f00;}
.zocial.google:before {content: "G";}
.zocial.googleplay:before {content: "h";}
.zocial.googleplus:before {content: "+";}
.zocial.gowalla:before {content: "@";}
.zocial.grooveshark:before {content: "8";}
.zocial.guest:before {content: "?";}
.zocial.html5:before {content: "5";}
.zocial.ie:before {content: "6";}
.zocial.instagram:before {content: "\00DC";}
.zocial.instapaper:before {content: "I";}
.zocial.intensedebate:before {content: "{";}
.zocial.itunes:before {content: "i"; color: #1a6dd2;}
.zocial.klout:before {content: "K"; }
.zocial.lanyrd:before {content: "-";}
.zocial.lastfm:before {content: "l";}
.zocial.lego:before {content: "\00EA"; color:#fff900;}
.zocial.linkedin:before {content: "L";}
.zocial.lkdto:before {content: "\00EE";}
.zocial.logmein:before {content: "\00EB";}
.zocial.macstore:before {content: "^";}
.zocial.meetup:before {content: "M";}
.zocial.myspace:before {content: "_";}
.zocial.ninetyninedesigns:before {content: "9"; color: #f50;}
.zocial.openid:before {content: "o"; color: #ff921d;}
.zocial.opentable:before {content: "\00C7";}
.zocial.paypal:before {content: "$";}
.zocial.pinboard:before {content: "n";}
.zocial.pinterest:before {content: "1";}
.zocial.plancast:before {content: "P";}
.zocial.plurk:before {content: "j";}
.zocial.pocket:before {content: "\00E7"; color:#ee4056;}
.zocial.podcast:before {content: "`";}
.zocial.posterous:before {content: "~";}
.zocial.print:before {content: "\00D1";}
.zocial.quora:before {content: "q";}
.zocial.reddit:before {content: ">"; color: red;}
.zocial.rss:before {content: "R";}
.zocial.scribd:before {content: "}"; color: #00d5ea;}
.zocial.skype:before {content: "S";}
.zocial.smashing:before {content: "*";}
.zocial.songkick:before {content: "k";}
.zocial.soundcloud:before {content: "s";}
.zocial.spotify:before {content: "=";}
.zocial.stackoverflow:before {content: "\00EC"; color: #ff7a15;}
.zocial.statusnet:before {content: "\00E2"; color: #fff;}
.zocial.steam:before {content: "b";}
.zocial.stripe:before {content: "\00A3";}
.zocial.stumbleupon:before {content: "/";}
.zocial.tumblr:before {content: "t";}
.zocial.twitter:before {content: "T";}
.zocial.viadeo:before {content: "H"; color: #f59b20;}
.zocial.vimeo:before {content: "V";}
.zocial.vk:before {content: "N";}
.zocial.weibo:before {content: "J"; color: #e6162d;}
.zocial.wikipedia:before {content: ",";}
.zocial.windows:before {content: "W";}
.zocial.wordpress:before {content: "w";}
.zocial.xing:before {content: "X"}
.zocial.yahoo:before {content: "Y";}
.zocial.ycombinator:before {content: "\00ED";}
.zocial.yelp:before {content: "y";}
.zocial.youtube:before {content: "U";}

/* Button background and text color */

.zocial.acrobat {background-color: #fff; color: #000;}
.zocial.amazon {background-color: #ffad1d; color: #030037; text-shadow: 0 1px 0 rgba(255,255,255,0.5);}
.zocial.android {background-color: #a4c639;}
.zocial.angellist {background-color: #000;}
.zocial.aol {background-color: #f00;}
.zocial.appnet {background-color: #3178bd;}
.zocial.appstore {background-color: #000;}
.zocial.bitbucket {background-color: #205081;}
.zocial.bitcoin {background-color: #efefef; color: #4d4d4d;}
.zocial.blogger {background-color: #ee5a22;}
.zocial.buffer {background-color: #232323;}
.zocial.call {background-color: #008000;}
.zocial.cal {background-color: #d63538;}
.zocial.cart {background-color: #333;}
.zocial.chrome {background-color: #006cd4;}
.zocial.cloudapp {background-color: #fff; color: #312c2a;}
.zocial.creativecommons {background-color: #000;}
.zocial.delicious {background-color: #3271cb;}
.zocial.digg {background-color: #164673;}
.zocial.disqus {background-color: #5d8aad;}
.zocial.dribbble {background-color: #ea4c89;}
.zocial.dropbox {background-color: #fff; color: #312c2a;}
.zocial.drupal {background-color: #0077c0; color: #fff;}
.zocial.dwolla {background-color: #e88c02;}
.zocial.email {background-color: #f0f0eb; color: #312c2a;}
.zocial.eventasaurus {background-color: #192931; color: #fff;}
.zocial.eventbrite {background-color: #ff5616;}
.zocial.eventful {background-color: #fff; color: #47ab15;}
.zocial.evernote {background-color: #6bb130; color: #fff;}
.zocial.facebook {background-color: #4863ae;}
.zocial.fivehundredpx {background-color: #333;}
.zocial.flattr {background-color: #8aba42;}
.zocial.flickr {background-color: #ff0084;}
.zocial.forrst {background-color: #1e360d;}
.zocial.foursquare {background-color: #44a8e0;}
.zocial.github {background-color: #fbfbfb; color: #050505;}
.zocial.gmail {background-color: #efefef; color: #222;}
.zocial.google {background-color: #4e6cf7;}
.zocial.googleplay {background-color: #000;}
.zocial.googleplus {background-color: #dd4b39;}
.zocial.gowalla {background-color: #ff720a;}
.zocial.grooveshark {background-color: #111; color:#eee;}
.zocial.guest {background-color: #1b4d6d;}
.zocial.html5 {background-color: #ff3617;}
.zocial.ie {background-color: #00a1d9;}
.zocial.instapaper {background-color: #eee; color: #222;}
.zocial.instagram {background-color: #3f729b;}
.zocial.intensedebate {background-color: #0099e1;}
.zocial.klout {background-color: #e34a25;}
.zocial.itunes {background-color: #efefeb; color: #312c2a;}
.zocial.lanyrd {background-color: #2e6ac2;}
.zocial.lastfm {background-color: #dc1a23;}
.zocial.lego {background-color: #fb0000;}
.zocial.linkedin {background-color: #0083a8;}
.zocial.lkdto {background-color: #7c786f;}
.zocial.logmein {background-color: #000;}
.zocial.macstore {background-color: #007dcb}
.zocial.meetup {background-color: #ff0026;}
.zocial.myspace {background-color: #000;}
.zocial.ninetyninedesigns {background-color: #fff; color: #072243;}
.zocial.openid {background-color: #f5f5f5; color: #333;}
.zocial.opentable {background-color: #990000;}
.zocial.paypal {background-color: #fff; color: #32689a; text-shadow: 0 1px 0 rgba(255,255,255,0.5);}
.zocial.pinboard {background-color: blue;}
.zocial.pinterest {background-color: #c91618;}
.zocial.plancast {background-color: #e7ebed; color: #333;}
.zocial.plurk {background-color: #cf682f;}
.zocial.pocket {background-color: #fff; color: #777;}
.zocial.podcast {background-color: #9365ce;}
.zocial.posterous {background-color: #ffd959; color: #bc7134;}
.zocial.print {background-color: #f0f0eb; color: #222; text-shadow: 0 1px 0 rgba(255,255,255,0.8);}
.zocial.quora {background-color: #a82400;}
.zocial.reddit {background-color: #fff; color: #222;}
.zocial.rss {background-color: #ff7f25;}
.zocial.scribd {background-color: #231c1a;}
.zocial.skype {background-color: #00a2ed;}
.zocial.smashing {background-color: #ff4f27;}
.zocial.songkick {background-color: #ff0050;}
.zocial.soundcloud {background-color: #ff4500;}
.zocial.spotify {background-color: #60af00;}
.zocial.stackoverflow {background-color: #fff; color: #555;}
.zocial.statusnet {background-color: #829d25;}
.zocial.steam {background-color: #000;}
.zocial.stripe {background-color: #2f7ed6;}
.zocial.stumbleupon {background-color: #eb4924;}
.zocial.tumblr {background-color: #374a61;}
.zocial.twitter {background-color: #46c0fb;}
.zocial.viadeo {background-color: #fff;  color: #000;}
.zocial.vimeo {background-color: #00a2cd;}
.zocial.vk {background-color: #45688E;}
.zocial.weibo {background-color: #faf6f1; color: #000;}
.zocial.wikipedia {background-color: #fff; color: #000;}
.zocial.windows {background-color: #0052a4; color: #fff;}
.zocial.wordpress {background-color: #464646;}
.zocial.xing {background-color: #0a5d5e;}
.zocial.yahoo {background-color: #a200c2;}
.zocial.ycombinator {background-color: #ff6600;}
.zocial.yelp {background-color: #e60010;}
.zocial.youtube {background-color: #f00;}

/*
The Miscellaneous Buttons
These button have no icons and can be general purpose buttons while ensuring consistent button style
Credit to @guillermovs for suggesting
*/

.zocial.primary, .zocial.secondary {margin: 0.1em 0; padding: 0 1em;}
.zocial.primary:before, .zocial.secondary:before {display: none;}
.zocial.primary {background-color: #333;}
.zocial.secondary {background-color: #f0f0eb; color: #222; text-shadow: 0 1px 0 rgba(255,255,255,0.8);}

/* Any browser-specific adjustments */

button:-moz-focus-inner {
	border: 0;
	padding: 0;
}

/* Reference icons from font-files
** Base 64-encoded version recommended to resolve cross-site font-loading issues
*/

@font-face {
    font-family: 'zocial';
    src: url('@{fonts-path}/zocial/zocial-regular-webfont.eot');
}
@font-face {
    font-family: 'zocial';
    src: url('@{fonts-path}/zocial/zocial-regular-webfont.ttf') format('truetype'),
         url('@{fonts-path}/zocial/zocial-regular-webfont.svg#zocialregular') format('svg');
    font-weight: normal;
    font-style: normal;
}