<?php

namespace App\Console\Commands;

use App\Models\SessionReminder;
use Illuminate\Console\Command;
use App\Models\Sessions;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class SendSessionReminderEmails extends Command
{
    protected $signature = 'sessions:send-reminders';
    protected $description = 'Send reminder emails 75 minutes before session starts';

    public function handle()
    {
        // Şu andan 75 dakika içerisinde başlayacak oturumları bul
        $nowTime = Carbon::now();
        $targetTimeStart = $nowTime->copy()->second(0);
        $targetTimeEnd = $nowTime->copy()->addMinutes(75)->second(59);

        $sessions = Sessions::whereBetween('start_date', [$targetTimeStart, $targetTimeEnd])
            ->whereNotIn('id', function ($query) {
                $query->select('sessions_id')
                    ->from('session_reminders')
                    ->where('reminder_type', '75_min');
            })
            ->get();

        $this->info("Found " . $sessions->count() . " sessions starting in 75 minutes that need reminders.");

        foreach ($sessions as $session) {
            // Oturumla ilgili test kullanıcılarını al
            $testUsers = $session->test_user;
            $this->info("Found " . $testUsers->count() . " test users for session id: {$session->id}");

            if ($testUsers->count() > 0) {
                foreach ($testUsers as $testUser) {
                    Mail::to($testUser->email)->send(new \App\Mail\SessionReminder($session));
                    $this->info("Sent reminder email to {$testUser->email} for session {$session->id}");
                }

                $this->info("Reminder record created for session {$session->id}");
            } else {
                $this->warn("No users found for session {$session->id}");
            }

            // Hatırlatma kaydını oluştur
            SessionReminder::create([
                'sessions_id' => $session->id,
                'reminder_type' => '75_min',
                'sent_at' => Carbon::now()
            ]);
        }
    }
}