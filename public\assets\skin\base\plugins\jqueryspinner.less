/*===============================================
  jQuery Spinner
================================================= */
.ui-spinner-input {
    color: inherit;
    min-height: 36px;
}
.ui-spinner-button {
    z-index: 3;
    cursor: pointer;
    display: block;
    overflow: hidden;
    position: absolute;
    right: 0;
    width: 16px;
    height: 50%;
    padding: 0;
    margin: 0;
    font-size: .5em;
    text-align: center;
}
.input-group .ui-spinner .form-control:first-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
.ui-spinner .ui-icon {
    position: absolute;
    margin-top: -2px;
    top: 50%;
    left: 0;
    text-indent: 0;
}
.ui-spinner-up .ui-icon {
    margin-top: -6px
}
.ui-spinner-up {
    top: 0
}
.ui-spinner-down {
    bottom: 0
}

/* TR overrides */
.ui-spinner .ui-icon-triangle-1-s {
    
    /* needed to correct false icon sprite pos */
    background-position: -65px -16px;
}
