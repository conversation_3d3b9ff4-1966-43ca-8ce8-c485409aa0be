$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});


// breakout modalı kapandığında scroll sorunu
$('#breakoutListModal').on('show.bs.modal', function () {
    $('#editSessionModal').css('overflow-y', 'hidden');
});
$('#breakoutListModal').on('hidden.bs.modal', function () {
    $('#editSessionModal').css('overflow-y', 'auto');
});

// breakout_check checkbox'ı tıklandığında işaretlenmeyecek
document.addEventListener('DOMContentLoaded', function() {
    var breakoutCheck = document.getElementById('breakout_check');

    breakoutCheck.addEventListener('click', function(event) {
        event.preventDefault();
    });
});

function check_breakout_func() {
    $('#breakoutListModal').modal('show');

    getBreakoutListAjax();
}

function breakoutHideDiv(){
    /*$('#editBreakoutDiv').hide();*/
    $('#newBreakoutDiv').hide();
}

// Add Form
$('#project_manager_custom').multiselect('refresh');
$('#test_user_custom').multiselect('refresh');
$('#multiselectTercumeBreakout').multiselect('refresh');

// Function to initialize breakout datepickers safely
function initializeBreakoutDatepickers() {
    var startDateTextBoxBreakout = $('#datetimepicker1breakout');
    var endDateTextBoxBreakout = $('#datetimepicker2breakout');
    var startDateTextBoxBreakoutEdit = $('#datetimepicker1breakoutedit');
    var endDateTextBoxBreakoutEdit = $('#datetimepicker2breakoutedit');

    // Only initialize if elements exist and are not already initialized
    if (startDateTextBoxBreakout.length && endDateTextBoxBreakout.length && !startDateTextBoxBreakout.hasClass('hasDatepicker')) {
        try {
            // Remove any existing masks first
            startDateTextBoxBreakout.unmask();
            endDateTextBoxBreakout.unmask();

            // Clear any invalid values before initialization
            startDateTextBoxBreakout.val('');
            endDateTextBoxBreakout.val('');

            $.timepicker.datetimeRange(
                startDateTextBoxBreakout,
                endDateTextBoxBreakout,
                {
                    minInterval: (1000 * 60 * 30), // 1hr
                    dateFormat: "dd/mm/yy",
                    timeFormat: "HH:mm",
                    defaultDate: "+1w",
                    changeYear: true,
                    changeMonth: true,
                    numberOfMonths: 1,
                    timeText: 'Zaman',
                    hourText: 'Saat',
                    minuteText: 'Dakika',
                    start: {}, // start picker options
                    end: {}, // end picker options
                    nextText: 'İleri',
                    prevText: 'Geri'
                }
            );
        } catch (e) {
            console.log('Error initializing breakout add datepickers:', e);
        }
    }

    if (startDateTextBoxBreakoutEdit.length && endDateTextBoxBreakoutEdit.length && !startDateTextBoxBreakoutEdit.hasClass('hasDatepicker')) {
        try {
            // Remove any existing masks first
            startDateTextBoxBreakoutEdit.unmask();
            endDateTextBoxBreakoutEdit.unmask();

            // Clear any invalid values before initialization
            startDateTextBoxBreakoutEdit.val('');
            endDateTextBoxBreakoutEdit.val('');

            $.timepicker.datetimeRange(
                startDateTextBoxBreakoutEdit,
                endDateTextBoxBreakoutEdit,
                {
                    minInterval: (1000 * 60 * 30), // 1hr
                    dateFormat: "dd/mm/yy",
                    timeFormat: "HH:mm",
                    defaultDate: "+1w",
                    changeYear: true,
                    changeMonth: true,
                    numberOfMonths: 1,
                    timeText: 'Zaman',
                    hourText: 'Saat',
                    minuteText: 'Dakika',
                    start: {}, // start picker options
                    end: {}, // end picker options
                    nextText: 'İleri',
                    prevText: 'Geri'
                }
            );
        } catch (e) {
            console.log('Error initializing breakout edit datepickers:', e);
        }
    }
}

// Initialize datepickers when modal is shown
$('#breakoutListModal').on('shown.bs.modal', function () {
    setTimeout(function() {
        initializeBreakoutDatepickers();
    }, 100);
});

// Add New Breakout butonu tıklandığında
$(document).on('click', '#newBreakoutShowButton', function() {
    $('#newBreakoutDiv').toggle();

    // Eğer div görünür hale geldiyse, form'a odaklan ve datepicker'ları initialize et
    if ($('#newBreakoutDiv').is(':visible')) {
        setTimeout(function() {
            // Initialize datepickers for the add form
            initializeBreakoutDatepickers();
            // addBreakout form'undaki ilk input'a odaklan
            $('#newBreakoutDiv input[name="title"]').focus();
        }, 300);
    }
});

// Breakout card oluşturma fonksiyonu
function createBreakoutCard(breakout) {
    clearNewBreakoutForm();
    var session_id = $('#breakoutListModal input[name="session_id"]').val();

    var urlDisplay = breakout.url ?
        '<a href="' + breakout.url + '" target="_blank" class="breakout-url">' +
            '<i class="fa fa-external-link" style="margin-right: 5px;"></i>' +
            (breakout.url.length > 30 ? breakout.url.substring(0, 30) + '...' : breakout.url) +
        '</a>' : '-';

    return '<div class="col-md-12 col-sm-12 mb-3">' +
        '<div class="panel panel-default breakout-card">' +
            '<div class="breakout-card-header">' +
                '<h5>' +
                    '<i class="fa fa-video-camera" style="margin-right: 8px;"></i>' +
                    (breakout.title || 'Başlık Yok') +
                '</h5>' +
            '</div>' +
            '<div class="breakout-card-body">' +
                '<div class="row">' +
                    '<div class="col-sm-6">' +
                        '<div class="breakout-info-label">Yayın Kodu:</div>' +
                        '<div class="breakout-info-value breakout-stream-id">' + (breakout.stream_id || '-') + '</div>' +
                        '<div class="breakout-info-label">Başlangıç:</div>' +
                        '<div class="breakout-info-value">' +
                            '<i class="fa fa-calendar breakout-date-icon breakout-date-start"></i>' +
                            breakout.start_date +
                        '</div>' +
                    '</div>' +
                    '<div class="col-sm-6">' +
                        '<div class="breakout-info-label">URL:</div>' +
                        '<div class="breakout-info-value">' + urlDisplay + '</div>' +
                        '<div class="breakout-info-label">Bitiş:</div>' +
                        '<div class="breakout-info-value">' +
                            '<i class="fa fa-calendar breakout-date-icon breakout-date-end"></i>' +
                            breakout.end_date +
                        '</div>' +
                    '</div>' +
                '</div>' +
                '<div class="breakout-card-footer">' +
                    '<a href="/event/eventFormEdit/' + breakout.events_id + '?edit=' + breakout.id + '" target="_blank" class="breakout-detail-btn">' +
                        '<i class="fa fa-external-link" style="margin-right: 5px;"></i>' +
                        'Detay Görüntüle' +
                    '</a>' +
                '</div>' +
            '</div>' +
        '</div>' +
    '</div>';
}

function getBreakoutListAjax(){
    var session_id = $('#breakoutListModal input[name="session_id"]').val();
    var csrfToken = $('meta[name="csrf-token"]').attr('content');

    $.ajax({
        url: '/session/getBreakoutList',
        type: 'POST',
        data: {
            session_id: session_id,
            _token: csrfToken
        },
        success: function(response) {
            console.log(response);
            var cardsContainer = $('#breakoutCardsContainer');
            cardsContainer.empty(); // Clear existing cards

            if (response.length === 0) {
                $('#editSessionModal #breakout_check').prop('checked', false);
                cardsContainer.append(
                    '<div class="col-md-12">' +
                        '<div class="alert alert-info text-center">' +
                            '<i class="fa fa-info-circle"></i> Breakout Bulunamadı' +
                        '</div>' +
                    '</div>'
                );
            } else {
                response.forEach(function(breakout) {
                    // Format dates to dd/mm/yyyy HH:mm format
                    var formattedStartDate = breakout.start_date;
                    var formattedEndDate = breakout.end_date;

                    var card = createBreakoutCard(breakout, formattedStartDate, formattedEndDate);
                    cardsContainer.append(card);
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
        }
    });
}

function clearNewBreakoutForm() {
    // $('#breakoutAddForm').find('input, textarea').not('input[name="session_id"]').val('');
    // $('#breakoutAddForm').find('input[type="checkbox"]').prop('checked', false);

    $('#breakoutAddForm').find('input[name="title"]').val('');
    // Clear datepicker values specifically
    $('#datetimepicker1breakout, #datetimepicker2breakout').val('');
}

$('#breakoutAddForm').submit(function (e) {
    e.preventDefault();
    keyboard:false;
    $(this).find('em').text('');

    message.wait();
    // return false;
    $.ajax({
        url: "/session/newBreakout",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Breakout Eklendi',
                type: 'success'
            }).then(function () {
                /*window.location.reload();*/
                $('#newBreakoutDiv').hide();
                $('#editSessionModal #breakout_check').prop('checked', true);
                clearNewBreakoutForm(); // Eklendikten sonra formu temizle
                getBreakoutListAjax(); // Tabloyu tekrar doldur

            })
        },
        error: function (res) {
            var response = res.responseJSON;
            var fields = [
                'title',
                'start_date',
                'end_date',
                'url',
                'main_language',
                'project_manager'
            ];
            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=bradd" + fields[i] + "]").text(response[fields[i]][0]);
            }

            message.close();

            swal({
                title: 'Dikkat!',
                text: 'Formda doldurmanız gereken alanlar mevcut. Lütfen yeniden kontrol ediniz.',
                type: 'warning'
            });
        }
    });
});

/////////