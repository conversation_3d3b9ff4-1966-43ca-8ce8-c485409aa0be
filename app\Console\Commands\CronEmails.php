<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Models\Sessions;
use App\Models\Orders;
use App\Models\VideoEdit;

class CronEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:send {type}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Günlük / Haftalık Mail gönderimi';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $type = $this->argument('type');


        switch ($type) {
            case "daily":
                $days = [7, 3, 1];
                $maildurum = "";
                foreach ($days as $remain_days) {
                    $all_data = Sessions::mailCreate($remain_days);
                    if ($all_data) {
                        foreach ($all_data as $data) {
                            if (isset($data->start_datetime)) {
                                $maildurum = 1;
                                \Mail::send('emails.day_email_send', ['data' => $data], function ($mail) use ($data, $remain_days) {
                                    $mail
                                        ->to(env('VISTREAM_EMAIL_TO'))
                                        ->subject($data->session_title . ' Yayına ' . $remain_days . ' Gün Kaldı');
                                });
                            }
                        }
                    }
                }
                break;

            case "weekly":
                $date1 = date('Y/m/d H:i');
                $date2 = date('Y/m/d H:i', strtotime('+1 weeks'));

                $all_data = Sessions::weekmailcreate($date1, $date2);

                \Mail::send('emails.week_email_send', ['data' => $all_data, 'date1' => $date1, 'date2' => $date2], function ($mail) use ($date1, $date2) {
                    $mail
                        ->to(env('VISTREAM_EMAIL_TO'))
                        ->subject(date('d/m/Y H:i', strtotime($date1)) . '-' . date('d/m/Y H:i ', strtotime($date2)) . ' Tarihleri Arasındaki Yayınlar');
                });

                break;

            case "sendWaitWeekOrderInvoice":

                ////Teklife Cevap Bekleniyor” Statusundeki tüm tekliflerin e-mail hatırlatması olarak atılması
                $sendWaitAnswerOrder = Orders::weekOrderInfo();
                //Eğer hiçbir yayına atanmayan bir teklif var ise her hafta başı aşağıdaki hatırlatma e-maili gönderilmelidir.
                // Bu notifikasyonda etkinlik fatura durumu--> Faturalanmayı bekleyenler ve ilgili teklif durumu --> teklif onaylandı statusundeki kayıtlar
                $sendApprovedInvoiceOrder = Orders::weekInvoiceWaiting();
                // ‘Yayın Durum – Yapıldı’ ve ‘Fatura Durumu- Faturalanmadı’ statüsündeki kayıtlar
                $sessionNotInvoice = Sessions::getSessionNotInvoice();
                //Yayın Durumu --> Yapıldı ve o yayına ait Teklif Fatura durumu --> Faturalanmayı bekliyor
                $waitInvoiceOrder = Orders::weekOrderInvoiceInfo();


                \Mail::send('emails.all_order_invoice', ['waitInvoiceOrder' => $waitInvoiceOrder, 'sessionNotInvoice' => $sessionNotInvoice, 'sendApprovedInvoiceOrder' => $sendApprovedInvoiceOrder, 'sendWaitAnswerOrder' => $sendWaitAnswerOrder], function ($mail) {
                    $mail
                        ->to(env('PROJECT_EMAIL_TO'))
                        ->subject('Teklif & Faturalama Durumu');
                });
                break;

            case "sendMissingSessionInfo":
                //Yayın Bilgilerinde Eksiklik Varsa Eksik Bilgileri Mail Olarak Gönderecek
                $all_data = Sessions::missingSessionInfo();
                \Mail::send('emails.missing-session-info', ['data' => $all_data], function ($mail) {
                    $mail
                        ->to(env('PROJECT_EMAIL_TO'))
                        ->subject('Tamamlanması Gereken Bilgiler');
                });
                break;
            case "sendEditInfo":
                //Teslim tarihne son 2 gün kalan video editleri günde 1 defa mail atar.
                $all_data = VideoEdit::getVideoEditDeliveryDate(2);
                if (count($all_data)) {
                    \Mail::send('emails.video_edit_send_info', ['data' => $all_data], function ($mail) use ($all_data) {
                        $mail
                            ->to(env('VISTREAM_EMAIL_EDIT'))
                            ->subject('Edit Hatırlatma');
                    });
                }

                break;
            case "sendRememberWeekEditInfo":
                //Tamamlanması Gereken haftanın Editlerini mail atar. (Çaıştığı tarihden 7. güne kadar.)
                $all_data = VideoEdit::getVideoEditWeekEndDate(6);
                if (count($all_data)) {
                    \Mail::send('emails.video_edit_week_send_info', ['data' => $all_data], function ($mail) use ($all_data) {
                        $mail
                            ->to(env('VISTREAM_EMAIL_EDIT'))
                            ->subject('Haftalık Edit Listesi '.date('d-m-Y').' / '.date('d-m-Y', strtotime(date('Y-m-d') . "+6 days")));
                    });
                }

                break;
            case "sendRememberBackupInfo":
                //Oluşturulan yedekleme kayıtlarının durumu 5. gününde de "Yedeklenmesi Bekleniyor" ise hatırlatma maili gönderir.
                $v_video_backup_list = \DB::table('v_video_backup_list')
                ->where(\DB::raw('date(create_date)'), '=', \DB::raw('date(NOW() - INTERVAL 5 DAY)'))
                ->where('backup_status', 'Yedeklenmesi Bekleniyor')
                ->get();
                if (count($v_video_backup_list)) {
                    \Mail::send('emails.backup_remember_Info', ['data' => $v_video_backup_list], function ($mail) use ($v_video_backup_list) {
                        $mail
                            ->to(env('VISTREAM_EMAIL_EDIT'))
                            ->subject('Yedeklenme Bekleniyor!');
                    });
                }

                break;
            case "sendToCustomerInfo":
                //Tamamlandı statüsünde olan ve müşteriye gönderilmeyen kayıtları mail atar.
                $all_data = VideoEdit::getNotSendToCustomer();
                if (count($all_data)) {
                    \Mail::send('emails.send_to_customer_info', ['data' => $all_data], function ($mail) use ($all_data) {
                        $mail
                            ->to(env('PROJECT_EMAIL_TO'))
                            ->subject(' Müşteriye gönderim hatırlatma.');
                    });
                }

                break;

            case  "sendToSurveyWeek" :
                $survey_data = Sessions::getSurveyWeek();
                if (count($survey_data)){
                    \Mail::send('emails.survey_week_alert', ['data' => $survey_data], function ($mail) use ($survey_data) {
                        $mail
                            ->to(env('PROJECT_EMAIL_TO'))
                            ->subject('Haftalık Anket Gönderim Durumu Hatırlatması.');
                    });
                }
                break;
            default:
                echo 'İşlem Başarısız';
        }
    }
}
