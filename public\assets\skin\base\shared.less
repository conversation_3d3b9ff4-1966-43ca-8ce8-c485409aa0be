/*==================================================
  Shared/Global Styles
==================================================== */

html {
    height: 100%;
    background: #fff;
}
body {
    width: 100%;
    height: 100%;
    min-height: 1100px;
    margin: 0;
    padding: 0;
    background: #e9e9e9;
    -webkit-font-smoothing: antialiased;
    
    /* Fix for webkit rendering */
    -webkit-text-size-adjust: 100%;
    font-size-adjust: 100%;
}
#main {
    position: relative;
    min-height: 100%;
    background: @main-bg;
}

/* psuedo background */
#main:before {
    content: "";
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: @main-bg;
}
#content_wrapper {
    position: relative;
    left: 0px;
    margin-left: 230px;
}

/* Transitions */
body.onload-check .navbar,
body.onload-check .navbar-branding,
body.onload-check #sidebar_left,
body.onload-check #sidebar_right,
body.onload-check #content_wrapper,
body.onload-check #topbar {
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
