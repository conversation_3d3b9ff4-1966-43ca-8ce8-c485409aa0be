/* ========================================================================
 * bootstrap-tour - v0.10.1
 * http://bootstraptour.com
 * ========================================================================
 * Copyright 2012-2013 <PERSON>u
 *
 * ========================================================================
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ========================================================================
 */

.tour-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1100 !important;;
  background-color: #000;
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.tour-step-backdrop {
  position: relative;
  z-index: 1101 !important;
  background: inherit;
}
.tour-step-backdrop > td {
  position: relative;
  z-index: 1101 !important;;
}
.tour-step-background {
  position: absolute !important;
  z-index: 1100 !important;;
  background: inherit;
  border-radius: 6px;
}
.popover[class*="tour-"] {
  z-index: 1100;
}
.popover[class*="tour-"] .popover-navigation {
  padding: 9px 14px;
}
.popover[class*="tour-"] .popover-navigation *[data-role="end"] {
  float: right;
}
.popover[class*="tour-"] .popover-navigation *[data-role="prev"],
.popover[class*="tour-"] .popover-navigation *[data-role="next"],
.popover[class*="tour-"] .popover-navigation *[data-role="end"] {
  cursor: pointer;
}
.popover[class*="tour-"] .popover-navigation *[data-role="prev"].disabled,
.popover[class*="tour-"] .popover-navigation *[data-role="next"].disabled,
.popover[class*="tour-"] .popover-navigation *[data-role="end"].disabled {
  cursor: default;
}
.popover[class*="tour-"].orphan {
  position: fixed;
  margin-top: 0;
}
.popover[class*="tour-"].orphan .arrow {
  display: none;
}
