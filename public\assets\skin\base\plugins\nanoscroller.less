/*===============================================
  Nan<PERSON>roller
================================================= */

/** initial setup **/
.nano {
  position : relative;
  width    : 100%;
  height   : 100%;
  overflow : hidden;
}
.nano > .nano-content {
  position      : absolute;
  overflow-x    : hidden;
  overflow-y    : hidden;
  right         : 0;
  left          : 0;
  top           : 0;
  bottom        : 0;
}
.nano.has-scrollbar > .nano-content {
  overflow-y    : scroll;
  overflow-x    : hidden;
}
#sidebar_left.nano > .nano-content {
  margin-top: 60px;
}
.nano.affix > .nano-content:focus {
  outline: thin dotted;
}
.nano.affix > .nano-content::-webkit-scrollbar {
  display: none;
}
.affix.has-scrollbar > .nano-content::-webkit-scrollbar {
  display: block;
}
.nano.affix > .nano-pane {
  background : rgba(0,0,0,.18);
  position   : absolute;
  width      : 5px;
  right      : 0;
  top        : 0;
  bottom     : 0;
  visibility : hidden\9; /* Target only IE7 and IE8 with this hack */
  opacity    : .01;
  -webkit-transition    : .2s;
  -moz-transition       : .2s;
  -o-transition         : .2s;
  transition            : .2s;
  -moz-border-radius    : 0px;
  -webkit-border-radius : 0px;
  border-radius         : 0px;
}

.nano.affix > .nano-pane > .nano-slider {
  background: #999;
  position              : relative;
  margin                : 0;
  -moz-border-radius    : 0px;
  -webkit-border-radius : 0px;
  border-radius         : 0px;
}
.nano.affix:hover > .nano-pane, .nano-pane.active, .nano-pane.flashed {
  visibility : visible\9; /* Target only IE7 and IE8 with this hack */
  opacity    : 0.99;
}

/* Nano colors/contextuals - default color grey */
.nano.affix.nano-primary > .nano-pane > .nano-slider {
    background: @brand-primary;
}
.nano.affix.nano-success > .nano-pane > .nano-slider {
    background: @brand-success;
}
.nano.affix.nano-info > .nano-pane > .nano-slider {
    background: @brand-info;
}
.nano.affix.nano-warning > .nano-pane > .nano-slider {
    background: @brand-warning;
}
.nano.affix.nano-danger > .nano-pane > .nano-slider {
    background: @brand-danger;
}
.nano.affix.nano-alert > .nano-pane > .nano-slider {
    background: @brand-alert;
}
.nano.affix.nano-system > .nano-pane > .nano-slider {
    background: @brand-system;
}
.nano.affix.nano-dark > .nano-pane > .nano-slider {
    background: @brand-dark;
}