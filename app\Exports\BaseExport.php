<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class BaseExport implements FromArray, WithHeadings, ShouldAutoSize
{
    protected $data;
    protected $headings;

    public function __construct(array $data, array $headings = [])
    {
        $this->data = $data;
        $this->headings = $headings;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        if (!empty($this->headings)) {
            return $this->headings;
        }

        // If no headings provided, use keys from first row
        if (!empty($this->data) && is_array($this->data[0])) {
            return array_keys($this->data[0]);
        }

        return [];
    }
}
