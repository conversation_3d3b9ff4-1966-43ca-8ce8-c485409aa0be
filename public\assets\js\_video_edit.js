//File Upload İçin Yüklenen Ekran
function getFileInfo(file_id) {
    var modalDiv = $('#fileUploadForm').modal({
        backdrop: 'static',
        keyboard: false
    });

    $.ajax({
        url: '/order/show/' + file_id,
        type: 'GET',
        success: function (response) {
            $('#divid').html(response);
            modalDiv.find('input[name=orders_id] ').val(file_id);
            modalDiv.modal();
        },
        error: function (res) {
        }
    });

}


//Talep Tarihi Boşsa Modal Açılmasın
function talepControl() {
    var talepTarihi = $(':input[name="request_date"]').val();

    if (talepTarihi.length === 0) {
        alert('Lütfen Önce Talep Tarihi Seçiniz')
    } else {
        var modalDiv = $('#slaHesapla').modal({
            backdrop: 'static',
            keyboard: false
        });
    }
}

//Talep Tarihini Modal içerisindeki Forma Aktarma
$(':input[name="request_date"]').on('change', function () {

    var request_date = $(':input[name="request_date"]').val();

    $(':input[name="start_date"]').val(request_date);
});
//Talep Tarihiyle Beraber Gün Hesaplamalarının Döndüğü Sonuç
$('#slahesaplama').submit(function (e) {
    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.


    $.post('/project/slahesapla?' + $(this).serialize(), function (res) {
        $(':input[name="sla_date"]').val(res);
        if ($(':input[name="delivery_date"]').val() == "") {
            $(':input[name="delivery_date"]').val(res);
        }
        $('#slaHesapla').modal('hide');
    });

});

// Seçilenlere Göre Hareketlenme Başlangıcı
$('#checkboxDefault3').change(function () {
    if ($(this).prop('checked') == true) {
        $("#session_type").prop('disabled', true);
    } else {
        $("#session_type").prop('disabled', false);
    }
});

$('[name=session_type]').change(function () {
    if ($(this).val() == "") {
        $("#checkboxDefault3").prop('checked', true);
    } else {
        $("#checkboxDefault3").prop('checked', false);
    }
});

$('[name=status]').change(function () {
    if ($(this).val() == 4) {
        $(":input[name=end_date]").val("{{date('d/m/Y')}}");
    } else {
        $(":input[name=end_date]").val("");
    }
});





// Seçilenlere Göre Hareketlenme Başlangıcı


var first_time_selected_video_type = true;
$('[name=edit_type]').on('change', function (e) {
    e.preventDefault();
    $('[name=video_type]').html('<option value="">Seçiniz</option>');
    $.ajax({
        url: "/project/getProjectType",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {

            var this_id = -1;

            if (first_time_selected_video_type) {
                if( backupData.video_project_id !== null ) {
                    this_id = backupData.video_project_id;
                } else if( backupData.events_id !== null ) {
                    this_id = backupData.events_id;
                }

                first_time_selected_video_type = false;
            }



            if (response.data.length) {
                for (var i in response.data) {

                    var selected = '';
                    if (this_id === response.data[i].id) {
                        selected = 'selected';
                    }

                    $('[name=video_type]').append(
                        '<option value="' + response.data[i].id + '" '+selected+'>' + response.data[i].name + '</option>'
                    );
                }

                $('[name=video_type]').change();
            }
        }
    });

});
$('[name=edit_type]').change();


var first_time_selected_edit_type = true;

// Yayınları Seçmek İçin Kullanıyoruz
$('[name="video_type"]').on('change', function (e) {
    e.preventDefault();

    var data = {
        'edit_type': $('[name=edit_type]').val(),
        'video_type': $(this).val(),
    };

    $('[name=session_type]').html('<option value="">Seçiniz</option>');
    // $.ajax({
    //     url: "/project/getSessionType",
    //     type: 'POST',
    //     data: data,
    //     dataType: 'json',
    //     success: function (response) {



    //         if (response.data.length) {
    //             for (var i in response.data) {

    //                 var selected = '';
    //                 if(first_time_selected_edit_type && backupData.session_id == response.data[i].id) {
    //                     selected = 'selected';
    //                 }

    //                 $('[name=session_type]').append('<option value="' + response.data[i].id + '" '+selected+'>' + response.data[i].title + '</option>');
    //             }

    //             first_time_selected_edit_type = false;




    //         }
    //     }
    // });

})


//Attachment Dosyaların Ekleneceği Alan

    var rows = 1;

    function attachment_fields() {

        rows++;
        var objTo = document.getElementById('attachment_fields')
        var divtest = document.createElement("div");
        divtest.setAttribute("class", "form-group removeclass" + rows);
        var rdiv = 'removeclass' + rows;
        divtest.innerHTML = ' <div class="col-sm-10 attachment_rows">' +
            '<label for="">Dosya Ekle</label>' +
            '<input type="file" name="attachment_file[]" class="form-control" value="Yükle"></div>' +
            ' <div class="input-group-btn" style="padding-top: 16px;">' +
            '<button class="btn btn-danger" type="button" onclick="remove_attachment_fields(' + rows + ');"> ' +
            'Sil</span>' +
            ' </button></div><div class="clear"></div>';

        objTo.appendChild(divtest)
    }

    function remove_attachment_fields(rid) {
        $('.removeclass' + rid).remove();
    }


// {{--//Video Dosyaları Ekleme Yeri--}}

var rows2 = 1;

function videos_fields() {

    rows2++;
    var objTo = document.getElementById('videos_fields')
    var divtest = document.createElement("div");
    divtest.setAttribute("class", "form-group removeclass" + rows2);
    var rdiv = 'removeclass' + rows2;
    divtest.innerHTML = ' <div class="col-sm-10 ve videos_rows" >' +
        '<label for="">Video Ekle</label>' +
        '<input type="text" name="video_name[]" class="form-control"> <input type="hidden" name="video_id[]"></div>' +
        ' <div class="input-group-btn" style="padding-top: 16px;">' +
        '<button class="btn btn-danger" type="button" onclick="remove_videos_fields(' + rows2 + ');"> ' +
        'Sil</span>' +
        ' </button></div><div class="clear"></div>';

    objTo.appendChild(divtest)
}

function remove_videos_fields(rid) {
    $('.removeclass' + rid).remove();
}