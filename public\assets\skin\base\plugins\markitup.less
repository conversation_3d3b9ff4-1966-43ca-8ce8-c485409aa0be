/*===============================================
  Markitup
================================================= */

/* Container */
.markItUp {
    width: 100%;
    margin: 0;
}

/* Header */
.markItUpHeader {
    min-height: 43px;
    padding: 8px 10px 0px;
    background: #fafafa;
    border: none;
    border-bottom: 1px solid #e7e7e7;
}
.markItUpButton {
    padding: 4px;
    background: #FFF;
    border: 1px solid #DDD;
}
.markItUpButton + .markItUpButton {
    border-left: 0;
}
.markItUpHeader ul .markItUpSeparator {
    height: 25px;
    background-color: #EEE;
}

/* Textarea */
.markItUpEditor {
    font-family: 'Open Sans', Helvetica, Arial, sans-serif;
    font-size: 13px;
    padding: 10px;
    border: none;
    width: 100%;
    height: 250px;
}

/* Footer */
.markItUpFooter {
    height: 15px;
}

/* preview frame */
.markItUpPreviewFrame {
    margin: 0;
    outline: 0;
    border: 0;
    padding: 5px 8px;
    border-top: 1px solid #e7e7e7;
    background: #fafafa;
}
