/**
 * Created by <PERSON><PERSON>DOGAN
 */

$(function() {
    //datatable defaults
    $.extend( true, $.fn.dataTable.defaults, {

        processing: true,
        serverSide: true,
        ajax: window.location.pathname,
        aLengthMenu: [[20, 50, 75, -1], [20, 50, 75, "Tümü"]],
        iDisplayLength: 20,
        lengthChange: true,
        searching: true,
        ordering: true,
        info: true,
        autoWidth: false,
        language: {
            sDecimal:        ",",
            sEmptyTable:     "Tabloda herhangi bir veri mevcut değil",
            sInfo:           "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
            sInfoEmpty:      "Kayıt yok",
            sInfoFiltered:   "(_MAX_ kayıt içerisinden bulunan)",
            sInfoPostFix:    "",
            sInfoThousands:  ".",
            sLengthMenu:     "Sayfada _MENU_ kayıt göster",
            sLoadingRecords: "Yükleniyor...",
            sProcessing:     "İşleniyor...",
            sSearch:         "Ara:",
            sZeroRecords:    "Eşleşen kayıt bulunamadı",
            oPaginate: {
                sFirst:    "İlk",
                sLast:     "Son",
                sNext:     "Sonraki",
                sPrevious: "Önceki"
            },
            oAria: {
                sSortAscending:  ": artan sütun sıralamasını aktifleştir",
                sSortDescending: ": azalan sütun soralamasını aktifleştir"
            }
        }
    });

    //tr character sorting fix!
    $.extend( $.fn.dataTableExt.oSort, {
        "turkish-pre": function ( a ) {
            var special_letters = {
                "C": "Ca", "c": "ca", "Ç": "Cb", "ç": "cb",
                "G": "Ga", "g": "ga", "Ğ": "Gb", "ğ": "gb",
                "I": "Ia", "ı": "ia", "İ": "Ib", "i": "ib",
                "O": "Oa", "o": "oa", "Ö": "Ob", "ö": "ob",
                "S": "Sa", "s": "sa", "Ş": "Sb", "ş": "sb",
                "U": "Ua", "u": "ua", "Ü": "Ub", "ü": "ub"
            };
            for (var val in special_letters)
                a = a.split(val).join(special_letters[val]).toLowerCase();
            return a;
        },
        "turkish-asc": function ( a, b ) {
            return ((a < b) ? -1 : ((a > b) ? 1 : 0));
        },
        "turkish-desc": function ( a, b ) {
            return ((a < b) ? 1 : ((a > b) ? -1 : 0));
        }
    });

}); // doc. ready