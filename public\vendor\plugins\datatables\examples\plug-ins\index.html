<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/demo.js"></script>

	<title>DataTables examples - Plug-ins</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>DataTables example <span>Plug-ins</span></h1>

			<div class="info">
				<p>While DataTables has a wide range of options and data type support built in, it can never cater for
				every type of data out of the box. For this reason, DataTables exposes an extension API which allows
				you, the developers using DataTables, to add support for your own data types, searching, ordering and
				feature plug-ins.</p>

				<p>The examples in this section show how plug-ins can be used and developed for DataTables.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Plug-ins</a></h3>
						<ul class="toc">
							<li><a href="./api.html">API plug-in methods</a></li>
							<li><a href="./sorting_auto.html">Ordering plug-ins (with type detection)</a></li>
							<li><a href="./sorting_manual.html">Ordering plug-ins (no type detection)</a></li>
							<li><a href="./range_filtering.html">Custom filtering - range search</a></li>
							<li><a href="./dom_sort.html">Live DOM ordering</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>