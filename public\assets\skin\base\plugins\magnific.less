    
/* ==================================================
  Magnific Popup
==================================================== */

/* Inline Content Styling (container) */
.modal-basic {
    max-width: 600px;
    margin: 40px auto;
    position: relative;
}
.modal-basic-bg {
    background: white;
    padding: 20px 30px;
    text-align: left;
    max-width: 600px;
    margin: 40px auto;
    position: relative;
}

/* Basic Example */
.mfp-no-margins img.mfp-img {
    padding: 0;
}
.mfp-no-margins .mfp-figure:after {
    top: 0;
    bottom: 0;
}
.mfp-no-margins .mfp-container {
    padding: 0;
}

/*Simple fade transition*/
.mfp-fade.mfp-bg {
    opacity: 0;
    -webkit-transition: all 0.15s ease-out;
    -moz-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.mfp-fade.mfp-bg.mfp-ready {
    opacity: 0.8;
}
.mfp-fade.mfp-bg.mfp-removing {
    opacity: 0;
}
.mfp-fade.mfp-wrap .mfp-content {
    opacity: 0;
    -webkit-transition: all 0.15s ease-out;
    -moz-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.mfp-fade.mfp-wrap.mfp-ready .mfp-content {
    opacity: 1;
}
.mfp-fade.mfp-wrap.mfp-removing .mfp-content {
    opacity: 0;
}

/* Fade-zoom animation for first dialog */

/* start state */
.my-mfp-zoom-in #small-dialog {
    opacity: 0;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
}

/* animate in */
.my-mfp-zoom-in.mfp-ready #small-dialog {
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}

/* animate out */
.my-mfp-zoom-in.mfp-removing #small-dialog {
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
    opacity: 0;
}

/* Dark overlay, start state */
.my-mfp-zoom-in.mfp-bg {
    opacity: 0;
    -webkit-transition: opacity 0.3s ease-out;
    -moz-transition: opacity 0.3s ease-out;
    -o-transition: opacity 0.3s ease-out;
    transition: opacity 0.3s ease-out;
}

/* animate in */
.my-mfp-zoom-in.mfp-ready.mfp-bg {
    opacity: 0.8;
}

/* animate out */
.my-mfp-zoom-in.mfp-removing.mfp-bg {
    opacity: 0;
}

/* Fade-move animation for second dialog */

/* at start */
.my-mfp-slide-bottom #small-dialog {
    opacity: 0;
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
    -o-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
    -webkit-transform: translateY(-20px) perspective( 600px) rotateX( 10deg);
    -moz-transform: translateY(-20px) perspective( 600px) rotateX( 10deg);
    -ms-transform: translateY(-20px) perspective( 600px) rotateX( 10deg);
    -o-transform: translateY(-20px) perspective( 600px) rotateX( 10deg);
    transform: translateY(-20px) perspective( 600px) rotateX( 10deg);
}

/* animate in */
.my-mfp-slide-bottom.mfp-ready #small-dialog {
    opacity: 1;
    -webkit-transform: translateY(0) perspective( 600px) rotateX( 0);
    -moz-transform: translateY(0) perspective( 600px) rotateX( 0);
    -ms-transform: translateY(0) perspective( 600px) rotateX( 0);
    -o-transform: translateY(0) perspective( 600px) rotateX( 0);
    transform: translateY(0) perspective( 600px) rotateX( 0);
}

/* animate out */
.my-mfp-slide-bottom.mfp-removing #small-dialog {
    opacity: 0;
    -webkit-transform: translateY(-10px) perspective( 600px) rotateX( 10deg);
    -moz-transform: translateY(-10px) perspective( 600px) rotateX( 10deg);
    -ms-transform: translateY(-10px) perspective( 600px) rotateX( 10deg);
    -o-transform: translateY(-10px) perspective( 600px) rotateX( 10deg);
    transform: translateY(-10px) perspective( 600px) rotateX( 10deg);
}

/* Dark overlay, start state */
.my-mfp-slide-bottom.mfp-bg {
    opacity: 0;
    -webkit-transition: opacity 0.3s ease-out;
    -moz-transition: opacity 0.3s ease-out;
    -o-transition: opacity 0.3s ease-out;
    transition: opacity 0.3s ease-out;
}

/* animate in */
.my-mfp-slide-bottom.mfp-ready.mfp-bg {
    opacity: 0.8;
}

/* animate out */
.my-mfp-slide-bottom.mfp-removing.mfp-bg {
    opacity: 0;
}
