
/*===============================================
  LAYOUT SETTINGS
================================================= */
/* If header is fixed modify primary containers padding */
.navbar.navbar-fixed-top + #sidebar_left + #content_wrapper {
	padding-top: 60px;
}

/* Fixed Sidebar */
#sidebar_left.affix { 
	position: fixed;
	height: 100%;
}

/* Scrollbar for Sidebars in Fixed Positions */
#sidebar_left.affix > .sidebar-menu {
	overflow-x: hidden;
	position: absolute;
	width: 100%;
	top: 85px;
	bottom: 52px;
	transition: top .2s ease-in-out;
}
#sidebar_left.affix > .email-menu + .sidebar-menu {
	overflow-x: hidden;
	position: absolute;
	width: 100%;
	top: 485px;
	bottom: 52px;
	transition: top .2s ease-in-out;
}

/* Corrects Pseudo sidebar bg when fixed */
#sidebar_left.affix:before {left: 0;}
body.sidebar-hidden #sidebar_left.affix:before,
body.sidebar-rtl #sidebar_left.affix:before {
	left: -230px;
}

/* Sidebar User Area - Hidden */
.user-info.hidden,
.user-info.hidden + .user-divider { display: none; }
body.usermenu-hidden .user-info,
body.usermenu-hidden .user-info + .user-divider { display: none }
body.usermenu-hidden #sidebar_left.affix .user-info + .user-divider + .sidebar-menu,
body.usermenu-hidden #sidebar_left.affix .user-info + .user-divider + .user-menu + .sidebar-menu {
	top: 0;
}
#sidebar_left.affix > .user-menu.usermenu-open + .sidebar-menu {
	top: 250px;
}

/* Breadcrumbs fixed state */
#topbar.affix {
	z-index: 1029; // should be below navbar but above most ui-widgets
	width: auto;
	left: 0;
	right: 0;
	margin-left: 230px;
	position: fixed;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
/* breadcrumb fixed settings when sidebar is minified */
body.sb-l-m #topbar.affix,
body.sb-l-o.sb-l-m #topbar.affix {
	margin-left: 60px;
}
body.sb-l-c #topbar.affix {
	margin-left: 0px;
}
/* if topbar is fixed grant proper margin spacing */
#topbar.affix + #content {
	margin-top: 51px;
}
/* if navbar is fixed and hidden grant proper margin spacing */
#topbar.affix.hidden + #content {
	margin-top: 0;
}
/* Topbar/Breadcrumbs Area - Hidden */
body.hidden-breadcrumbs #topbar { display: none }

/* for preview only - no real use in development enviroment */
body.sidebar-collapsed #sidebar:before { width: 40px }
