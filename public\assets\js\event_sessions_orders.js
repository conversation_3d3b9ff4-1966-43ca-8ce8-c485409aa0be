$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

var emAsiaCountryIds = ["13", "94", "95", "125", "149", "157", "164", "187", "203", "207", "227", "233"];
var emAsiaCountryIdsEm = ["94", "95", "125", "157", "164", "187", "203", "207", "227", "233"];
var emAsiaCountryIdsAuNz = ["13", "149"];


//Etkinlik Düzenleme Ajax Formu
$('#eventEditForm').submit(function (e) {
    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.
    message.wait();
    $.ajax({
        url: "/event/eventSessionForm",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Etkinlik Güncellendi',
                type: 'success'
            }).then(function () {
                window.location.reload();
            })
        },

        error: function (res) {
            var response = res.responseJSON;
            var fields = [
                'customer_name',
                'name',
                'series_name',
                'region',
                'responsible_name',
                'responsible_email',


            ];
            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=edit" + fields[i] + "]").text(response[fields[i]][0]);
            }
            message.close();
        }
    });
});
//
// $.ajaxSetup({
//     headers: {
//         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//     }
// });

function eventDelete(eventDeleteID) {
    swal({
        title: 'Lütfen Dikkat',
        text: 'Bu etkinliğe bağlı olan tüm yayın, teklifler ve Faturalar silinecektir, devam etmek istediğinize emin misiniz?',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ff0000',
        cancelButtonColor: '#00afff',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal Et'
    }).then(function () {
        $.ajax({
            url: "/event/eventDelete/" + eventDeleteID,
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function (response) {
                swal({
                    title: 'Etkinlik Silindi',
                    text: 'Silme işlemi Tamamlandı',
                    type: 'success'
                })
                window.location = '/event/list/';
            },
            error: function (response) {
                swal({
                    title: 'İşlem Yapılamadı',
                    text: 'Etkinlik Silinirken Hata Oluştu',
                    type: 'error'
                })
            }

        });

    })
}


////////////////////////////////////////////    EVENT   /////////////////////////////////////////////////////////////////


////////////////////////////////////////////  SESSİON  /////////////////////////////////////////////////////////////////

//Yeni Session Ekleme Formu
$('#sessionAddForm').submit(function (e) {
    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.

    $(this).find('em').text('');

    if(document.getElementById("servis_turu").value == "Destek"){
        if(!document.getElementById('yayin_oncesi').checked && !document.getElementById('yayin_esnasinda').checked){
            swal({
                title: 'Dikkat!',
                text: 'Lütfen yayın öncesi veya yayın sırası destek seçeneklerinden birini seçiniz.',
                type: 'warning'
            });
            return;
        }
    }

    message.wait();
    // return false;

    $.ajax({
        url: "/event/eventFormEditProcess",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Yayın Eklendi',
                type: 'success'
            }).then(function () {
                window.location.reload();
            })
        },
        error: function (res) {
            var response = res.responseJSON;

            var fields = [
                'title',
                'session_date',
                'start_date',
                'end_date',
                'url',
                'main_language',
                'subject',
                'yayin_lokasyonu',
                'project_manager',
                'room_number',
                'servis_turu',
                'webinar_cluster',
                'extra_field_2'
            ];

            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=new" + fields[i] + "]").text(response[fields[i]][0]);
            }
            message.close();

            swal({
                title: 'Dikkat!',
                text: 'Formda doldurmanız gereken alanlar mevcut. Lütfen yeniden kontrol ediniz.',
                type: 'warning'
            });
        }
    });

});
// $.ajaxSetup({
//     headers: {
//         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//     }
// });

/**
 * Ajax ERROR Global
 */
$( document ).ajaxError(function( event, jqxhr, settings, thrownError ) {
    if( jqxhr.status == 401 ) {

    	alert('Oturumunuz kapanmıştır.\nLütfen yeniden giriş yapınız.');
    	window.location.reload();
    }
});


//Session Yayın  Düzenlemede Modal Doldurma
function getSessionInfo(session_id) {
    message.wait();
    $.getJSON('/session/show/' + session_id, function (response) {
        var modalDiv = $('#editSessionModal').modal({
            backdrop: 'static',
            keyboard: false
        });

        $('#breakoutListModal input[name="session_id"]').val(response.session.id);


        selected_city_id = response.session.cities_id;
        console.log( response );
        //session title
        modalDiv.find('input[name=stream_id]').val(response.session.stream_id);
        modalDiv.find('input[name=title]').val(response.session.title);
        modalDiv.find('input[name=subject]').val(response.session.subject);
        modalDiv.find('input[name=customer_name_hidden]').val(response.session.customer_name);
        modalDiv.find('input[name=speaker_number]').val(response.session.speaker_number);
        modalDiv.find('input[name=event_id]').val(response.session.events_id);
        modalDiv.find('select[name=session_type]').val(response.session.session_type);
        modalDiv.find('input[name=session_date]').val(response.session.session_date);
        // modalDiv.find('input[name=session_time_fixed]').val(response.session.session_time_fixed);
        modalDiv.find('input[name=start_date]').val(response.session.start_date);
        modalDiv.find('input[name=end_date]').val(response.session.end_date);
        //                modalDiv.find('select[name=cities_id]').val(response.session.cities_id);
        //                modalDiv.find('select[name=country]').val(response.country.id).change();
        modalDiv.find('select[name=main_language]').val(response.session.languages_id);
        modalDiv.find('input[name=session_images]').val(response.session.session_images);
        modalDiv.find('select[name=session_status]').val(response.session.session_status);
        modalDiv.find('select[name=host_type]').val(response.session.host_type);
        modalDiv.find('select[name=host_country]').val(response.session.host_country);
        modalDiv.find('input[name=brand]').val(response.session.brand);
        modalDiv.find('input[name=business_owner_name]').val(response.session.business_owner_name);
        modalDiv.find('input[name=business_owner_email]').val(response.session.business_owner_email);
        modalDiv.find('select[name=webinar_cluster]').val(response.session.webinar_cluster);
        modalDiv.find('select[name=invoice_status]').val(response.session.invoice_status);
        modalDiv.find('select[name=yayin_lokasyonu]').val(response.session.yayin_lokasyonu);
        modalDiv.find('select[name=yayin_platformu]').val(response.session.yayin_platformu);
        modalDiv.find('select[name=servis_turu]').val(response.session.servis_turu);
        if (response.session.servis_turu !== 'Destek'){
            modalDiv.find('.servis_turu_check').addClass('hide')
        }

        if (emAsiaCountryIds.includes(response.session.host_country)) {
            modalDiv.find('.em_asia_country_div').removeClass('hide');
        }else {
            modalDiv.find('.em_asia_country_div').addClass('hide');
        }

        if(response.session.customer_name == "Pfizer OSS"){
            modalDiv.find('#pfizer_oss_area_brand').removeClass('hide');
            modalDiv.find('#edit_pfizer_oss_area_cluster_galaxy').removeClass('hide');
            modalDiv.find('#edit_pfizer_oss_area_cluster_combined').removeClass('hide');
        } if(response.session.customer_name == "Viatris"){
            modalDiv.find('#edit_viatris_area_cluster_connect').removeClass('hide');
        } else {
            //
        }

        modalDiv.find('input[name=url]').val(response.session.url);
        modalDiv.find('input[name=url_password]').val(response.session.url_password);
        modalDiv.find('textarea[name=snote]').val(response.session.note);
        modalDiv.find('textarea[name=report_note]').val(response.session.report_note);
        //                modalDiv.find('textarea[name=location]').val(response.session.location);
        modalDiv.find('input[name=unique_visitors]').val(response.session.unique_visitors);
        modalDiv.find('input[name=total_visitors]').val(response.session.total_visitors);
        modalDiv.find('input[name=register_count]').val(response.session.register_count);
        modalDiv.find('input[name=average_view]').val(response.session.average_view);
        modalDiv.find('input[name=country_count]').val(response.session.country_count);
        modalDiv.find('input[name=speciality_count]').val(response.session.speciality_count);

        modalDiv.find('input[name=survey_question_count]').val(response.session.survey_question_count);
        modalDiv.find('input[name=survey_answer_count]').val(response.session.survey_answer_count);

        if (response.session.survey_is_exist === 1){
            $('#surveyDetailInputs').removeClass('hide');
            $('input[name="is_survey_exist"]')[0].checked = true;
            $('input[name="is_survey_exist"]')[1].checked = false;
        }else if(response.session.survey_is_exist === 0){
            modalDiv.find('#surveyDetailInputs').addClass('hide');
            modalDiv.find('input[name="is_survey_exist"]')[0].checked = false;
            modalDiv.find('input[name="is_survey_exist"]')[1].checked = true;
        }

        modalDiv.find('input[name=video_url]').val(response.session.video_url);
        modalDiv.find('input[name=rapor_url]').val(response.session.rapor_url);
        modalDiv.find('input[name=yayin_suresi]').val(response.session.yayin_suresi);
        modalDiv.find('input[name=send_report_person]').val(response.session.send_report_person);
        modalDiv.find('input[name=extra_field_1]').val(response.session.extra_field_1);
        modalDiv.find('input[name=extra_field_2]').val(response.session.extra_field_2);
        modalDiv.find('input[name=extra_field_3]').val(response.session.extra_field_3);
        modalDiv.find('input[name=extra_field_4]').val(response.session.extra_field_4);
        modalDiv.find('input[name=custom_event_id]').val(response.session.custom_event_id);
        modalDiv.find('input[name=room_number]').val(response.session.room_number);

        //                modalDiv.find('input[name=expence_amount]').val(response.session.expence_amount);
        //                modalDiv.find('textarea[name=expence_desc]').val(response.session.expence_desc);
        modalDiv.find('input[name=id]').val(response.session.id);
        modalDiv.find('button[id=deleteSessionID]').val(response.session.id);
        //Tercüme dilleri
        var languages = [];
        response.languages.forEach(function (l) {
            languages.push(l.id)
        });
        modalDiv.find("#multiselectTercumeEdit").val(languages);
        modalDiv.find("#multiselectTercumeEdit").multiselect("refresh");

        var project_manager = [];
        response.project_manager.forEach(function (l) {
            project_manager.push(l.id)
        });
        modalDiv.find("#project_manager").val(project_manager);
        modalDiv.find("#project_manager").multiselect("refresh");

        var test_user = [];
        response.test_user.forEach(function (l) {
            test_user.push(l.id)
        });

        modalDiv.find('#locationList tbody').html('');
        response.locationDetails.forEach(function (l) {
            makeNewLocationRow(
                l.country_name,
                l.city_name,
                l.location_name,
                l.description,
                l.session_users,
                l.speakers,
                l.id,
                l.country_id,
                l.city_id,
                l.location_channel
            )
        });

        modalDiv.find("#test_user").val(test_user);
        modalDiv.find("#test_user").multiselect("refresh");

        var session_time_fixed = response.session.session_time_fixed;
        var satisfaction_survey = response.session.survey_sent_date;
        var survey_completed_date = response.session.survey_completed_date;
        var report_status = response.session.report_status;

        var green_curtain = response.session.green_curtain;
        var teleprompter = response.session.teleprompter;
        var audience = response.session.audience;

        var test_required = response.session.test_required;
        var yayin_oncesi = response.session.yayin_oncesi;
        var yayin_esnasinda = response.session.yayin_esnasinda;
        var dry_run_required = response.session.dry_run_required;
        var login_time_required = response.session.login_time_required;
        var em_asia_report_required = response.session.em_asia_report_required;
        var au_nz_report_required = response.session.au_nz_report_required;
        var register_report_required = response.session.register_report_required;
        var polling_mevcut = response.session.polling_mevcut;
        var video_yayinlanacak = response.session.video_yayinlanacak;
        var breakout_check = response.session.breakout_check;
        var certificate_mevcut = response.session.certificate_mevcut;
        var survey_mevcut = response.session.survey_mevcut;

        if(response.video_backup[0] != null) {
            modalDiv.find('input[name=video_backup_status]').prop('checked', true);
        }

        if (breakout_check == "1") {
            modalDiv.find('input[name=breakout_check]').prop('checked', true);
            // modalDiv.find(".room_number_div").removeClass("hide");
        }
        if (video_yayinlanacak == "1") {
            modalDiv.find('input[name=video_yayinlanacak]').prop('checked', true);
        }
        if (polling_mevcut == "1") {
            modalDiv.find('input[name=polling_mevcut]').prop('checked', true);
        }
        if (certificate_mevcut == "1") {
            modalDiv.find('input[name=certificate_mevcut]').prop('checked', true);
        }
        if (survey_mevcut == "1") {
            modalDiv.find('input[name=survey_mevcut]').prop('checked', true);
        }
        if (dry_run_required == "1") {
            modalDiv.find('input[name=dry_run_required]').prop('checked', true);
        }
        if (login_time_required == "1") {
            modalDiv.find('input[name=login_time_required]').prop('checked', true);
        }
        if (em_asia_report_required == "1") {
            modalDiv.find('input[name=em_asia_report_required]').prop('checked', true);
        }
        if (au_nz_report_required == "1") {
            modalDiv.find('input[name=au_nz_report_required]').prop('checked', true);
        }
        if (register_report_required == "1") {
            modalDiv.find('input[name=register_report_required]').prop('checked', true);
        }
        if (session_time_fixed == "1") {
            modalDiv.find('input[name=session_time_fixed]').prop('checked', true);
        }
        if (green_curtain == "1") {
            modalDiv.find('input[name=green_curtain]').prop('checked', true);
        }
        if (teleprompter == "1") {
            modalDiv.find('input[name=teleprompter]').prop('checked', true);
        }
        if (audience == "1") {
            modalDiv.find('input[name=audience]').prop('checked', true);
        }
        if (test_required == "1") {
            modalDiv.find('input[name=test_gerekli_check]').prop('checked', true);
            $(".test_gerekli_div").removeClass("hide");
        }else{
            modalDiv.find('input[name=test_gerekli_check]').prop('checked', false);
            modalDiv.find(".test_gerekli_div").addClass("hide");
        }

        if(response.session.servis_turu == "Destek"){
            modalDiv.find(".servis_turu_check").removeClass("hide");
        }
        if(response.session.session_type == "Online"){
            modalDiv.find(".yayin_platformu_div").removeClass("hide");
        }

        if (yayin_oncesi == "1") {
            modalDiv.find('input[name=yayin_oncesi]').prop('checked', true);
        }
        if (yayin_esnasinda == "1") {
            modalDiv.find('input[name=yayin_esnasinda]').prop('checked', true);
        }
        /*else{
            modalDiv.find('input[name=yayin_oncesi]').prop('checked', false);
            if (yayin_esnasinda != "1") {
                $(".servis_turu_check").addClass("hide");
            }
        }
        else{
            modalDiv.find('input[name=yayin_esnasinda]').prop('checked', false);
            if (yayin_oncesi != "1") {
                $(".servis_turu_check").addClass("hide");
            }
        }*/

        if (survey_completed_date != null) {
            modalDiv.find('input[name=survey_completed_date]').prop('checked', true);
        }

        if (satisfaction_survey != null) {
            modalDiv.find('#survey_sent_date').append('<i class="fa fa-thumbs-up" style="color: #0B792F;"></i> <strong>Memnuniyet Anketi Gönderildi</strong>');
            modalDiv.find('#checkboxDefault15').attr('disabled', false);

        }else {
            modalDiv.find('#survey_sent_date').append('<i class="fa fa-thumbs-down" style="color: #dc3519;"></i> <strong>Memnuniyet Anketi Gönderilmedi</strong>');
            modalDiv.find('#checkboxDefault15').attr('disabled', true);
        }

        if (report_status == "1") {
            modalDiv.find('input[name=report_status]').prop('checked', true);
        }
        modalDiv.modal();
    }).done(function () {
        message.close();
    })
        .fail(function (jqXHR, textStatus, errorThrown) {
            if (jqXHR.status != 401) {
                swal({
                    title: 'Bir Sorun Oluştu',
                    text: 'Yolunda Gitmeyen Birşeyler Var   Lütfen Yöneticiye Haber Veriniz ',
                    type: 'error'
                });
            }
        })
        .always(function () {
        });
}
// Yayın ekle, duzenle popupda secilen ulke Em Asia ise;
function showEmAsiaCountryDiv(){
    var hostCountrySelect = document.getElementById("host_country");
    var emAsiaCountryDiv = document.getElementById("em_asia_country_div");
    var emAsiaReportCheckbox = document.getElementById("em_asia_report_required_add_modal");
    var auNzReportCheckbox = document.getElementById("au_nz_report_required_add_modal");
    var registerReportCheckbox = document.getElementById("register_report_required_add_modal");

    var selectedCountryId = hostCountrySelect.value;

    if (emAsiaCountryIds.includes(selectedCountryId)) {
        emAsiaCountryDiv.classList.remove('hide');
        registerReportCheckbox.checked = true;
    } else {
        emAsiaCountryDiv.classList.add('hide');
        registerReportCheckbox.checked = false;
    }

    if (emAsiaCountryIdsEm.includes(selectedCountryId)) {
        emAsiaReportCheckbox.checked = true;
    } else {
        emAsiaReportCheckbox.checked = false;
    }

    if (emAsiaCountryIdsAuNz.includes(selectedCountryId)) {
        auNzReportCheckbox.checked = true;
    } else {
        auNzReportCheckbox.checked = false;
    }
}

function checkOtherCheckbox(sourceId, targetId) {
    var sourceCheckbox = document.getElementById(sourceId);
    var targetCheckbox = document.getElementById(targetId);

    if (sourceCheckbox.checked) {
        targetCheckbox.checked = true;
    }
}

//Yayın Güncelleme Ajax Formu
$('#sessionEditForm').submit(function (e) {

    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.
    keyboard:false;
    $(this).find('em').text('');

    if(document.getElementById("servis_turu").value == "Destek"){
        if(!document.getElementById('yayin_oncesi').checked && !document.getElementById('yayin_esnasinda').checked){
            swal({
                title: 'Dikkat!',
                text: 'Lütfen yayın öncesi veya yayın sırası destek seçeneklerinden birini seçiniz.',
                type: 'warning'
            });
            return;
        }
    }

    message.wait();
    // return false;
    $.ajax({
        url: "/session/sessionEditForm",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Yayın Güncellendi',
                type: 'success'
            }).then(function () {
                window.location.reload();
            })
        },
        error: function (res) {
            var response = res.responseJSON;
            var fields = [
                'title',
                'session_date',
                'start_date',
                'end_date',
                'cities_id',
                'location',
                'url',
                'main_language',
                'subject',
                'yayin_lokasyonu',
                'project_manager',
                'host_type',
                'host_country',
                'room_number',
                'servis_turu',
                'webinar_cluster',
                'brand',
                'business_owner_name',
                'business_owner_email',
                'register_count',
                'average_view',
                'yayin_suresi',
                'extra_field_2' //clickup_task_url
            ];
            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=edit" + fields[i] + "]").text(response[fields[i]][0]);
            }

            message.close();

            swal({
                title: 'Dikkat!',
                text: 'Formda doldurmanız gereken alanlar mevcut. Lütfen yeniden kontrol ediniz.',
                type: 'warning'
            });
        }
    });
});

// $.ajaxSetup({
//     headers: {
//         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//     }
// });

// Yayın çoğaltma modal doldurma
function getSessionInfoForDuplicate(session_id) {
    message.wait();
    $.getJSON('/session/show/' + session_id, function (response) {
        var modalDiv = $('#duplicateSessionModal').modal({
            backdrop: 'static',
            keyboard: false
        });

        selected_city_id = response.session.cities_id;
        console.log( response );
        //session title
        modalDiv.find('input[name=title]').val(response.session.title);
        modalDiv.find('input[name=subject]').val(response.session.subject);
        modalDiv.find('input[name=speaker_number]').val(response.session.speaker_number);
        modalDiv.find('input[name=event_id]').val(response.session.events_id);
        modalDiv.find('select[name=session_type]').val(response.session.session_type);
        modalDiv.find('input[name=session_date]').val(response.session.session_date);
        // modalDiv.find('input[name=session_time_fixed]').val(response.session.session_time_fixed);
        modalDiv.find('input[name=start_date]').val(response.session.start_date);
        modalDiv.find('input[name=end_date]').val(response.session.end_date);
        //                modalDiv.find('select[name=cities_id]').val(response.session.cities_id);
        //                modalDiv.find('select[name=country]').val(response.country.id).change();
        modalDiv.find('select[name=main_language]').val(response.session.languages_id);
        modalDiv.find('input[name=session_images]').val(response.session.session_images);
        modalDiv.find('select[name=session_status]').val(response.session.session_status);
        modalDiv.find('select[name=host_type]').val(response.session.host_type);
        modalDiv.find('select[name=host_country]').val(response.session.host_country);
        modalDiv.find('input[name=brand]').val(response.session.brand);
        modalDiv.find('input[name=business_owner_name]').val(response.session.business_owner_name);
        modalDiv.find('input[name=business_owner_email]').val(response.session.business_owner_email);
        modalDiv.find('select[name=webinar_cluster]').val(response.session.webinar_cluster);
        modalDiv.find('select[name=invoice_status]').val(response.session.invoice_status);
        modalDiv.find('select[name=yayin_lokasyonu]').val(response.session.yayin_lokasyonu);
        modalDiv.find('select[name=yayin_platformu]').val(response.session.yayin_platformu);
        modalDiv.find('select[name=servis_turu]').val(response.session.servis_turu);
        if (response.session.servis_turu !== 'Destek'){
            $('.servis_turu_check').addClass('hide')
        }

        if (emAsiaCountryIds.includes(response.session.host_country)) {
            modalDiv.find('.em_asia_country_div').removeClass('hide');
        }else {
            modalDiv.find('.em_asia_country_div').addClass('hide');
        }

        modalDiv.find('input[name=url]').val(response.session.url);
        modalDiv.find('input[name=url_password]').val(response.session.url_password);
        modalDiv.find('textarea[name=snote]').val(response.session.note);
        modalDiv.find('textarea[name=report_note]').val(response.session.report_note);
        //                modalDiv.find('textarea[name=location]').val(response.session.location);
        // modalDiv.find('input[name=unique_visitors]').val(response.session.unique_visitors);
        // modalDiv.find('input[name=total_visitors]').val(response.session.total_visitors);
        // modalDiv.find('input[name=register_count]').val(response.session.register_count);
        // modalDiv.find('input[name=average_view]').val(response.session.average_view);
        // modalDiv.find('input[name=country_count]').val(response.session.country_count);
        // modalDiv.find('input[name=speciality_count]').val(response.session.speciality_count);

        // modalDiv.find('input[name=survey_question_count]').val(response.session.survey_question_count);
        // modalDiv.find('input[name=survey_answer_count]').val(response.session.survey_answer_count);

        // if (response.session.survey_is_exist === 1){
        //     modalDiv.find('#surveyDetailInputs').removeClass('hide');
        //     modalDiv.find('input[name="is_survey_exist"]')[0].checked = true;
        //     modalDiv.find('input[name="is_survey_exist"]')[1].checked = false;
        // }else if(response.session.survey_is_exist === 0){
        //     modalDiv.find('#surveyDetailInputs').addClass('hide');
        //     modalDiv.find('input[name="is_survey_exist"]')[0].checked = false;
        //     modalDiv.find('input[name="is_survey_exist"]')[1].checked = true;
        // }

        // modalDiv.find('input[name=video_url]').val(response.session.video_url);
        // modalDiv.find('input[name=rapor_url]').val(response.session.rapor_url);
        // modalDiv.find('input[name=yayin_suresi]').val(response.session.yayin_suresi);
        // modalDiv.find('input[name=send_report_person]').val(response.session.send_report_person);
        modalDiv.find('input[name=extra_field_1]').val(response.session.extra_field_1);
        modalDiv.find('input[name=extra_field_2]').val(response.session.extra_field_2);
        modalDiv.find('input[name=extra_field_3]').val(response.session.extra_field_3);
        modalDiv.find('input[name=extra_field_4]').val(response.session.extra_field_4);
        modalDiv.find('input[name=custom_event_id]').val(response.session.custom_event_id);
        modalDiv.find('input[name=room_number]').val(response.session.room_number);

        //                modalDiv.find('input[name=expence_amount]').val(response.session.expence_amount);
        //                modalDiv.find('textarea[name=expence_desc]').val(response.session.expence_desc);
        modalDiv.find('input[name=id]').val(response.session.id);
        modalDiv.find('button[id=deleteSessionID]').val(response.session.id);
        //Tercüme dilleri
        var languages = [];
        response.languages.forEach(function (l) {
            languages.push(l.id)
        });
        modalDiv.find("#multiselectTercumeEdit").val(languages);
        modalDiv.find("#multiselectTercumeEdit").multiselect("refresh");

        var project_manager = [];
        response.project_manager.forEach(function (l) {
            project_manager.push(l.id)
        });
        modalDiv.find("#project_manager").val(project_manager);
        modalDiv.find("#project_manager").multiselect("refresh");

        var test_user = [];
        response.test_user.forEach(function (l) {
            test_user.push(l.id)
        });
        modalDiv.find("#test_user").val(test_user);
        modalDiv.find("#test_user").multiselect("refresh");

        var session_time_fixed = response.session.session_time_fixed;
        var satisfaction_survey = response.session.survey_sent_date;
        var survey_completed_date = response.session.survey_completed_date;
        var report_status = response.session.report_status;

        var green_curtain = response.session.green_curtain;
        var teleprompter = response.session.teleprompter;
        var audience = response.session.audience;

        var test_required = response.session.test_required;
        var yayin_oncesi = response.session.yayin_oncesi;
        var yayin_esnasinda = response.session.yayin_esnasinda;
        var dry_run_required = response.session.dry_run_required;
        var login_time_required = response.session.login_time_required;
        var em_asia_report_required = response.session.em_asia_report_required;
        var au_nz_report_required = response.session.au_nz_report_required;
        var register_report_required = response.session.register_report_required;
        var polling_mevcut = response.session.polling_mevcut;
        var video_yayinlanacak = response.session.video_yayinlanacak;
        var breakout_check = response.session.breakout_check;
        var certificate_mevcut = response.session.certificate_mevcut;
        var survey_mevcut = response.session.survey_mevcut;

        if(response.video_backup[0] != null) {
            modalDiv.find('input[name=video_backup_status]').prop('checked', true);
        }

        if (breakout_check == "1") {
            modalDiv.find('input[name=breakout_check]').prop('checked', true);
            // $(".room_number_div").removeClass("hide");
        }
        if (video_yayinlanacak == "1") {
            modalDiv.find('input[name=video_yayinlanacak]').prop('checked', true);
        }
        if (polling_mevcut == "1") {
            modalDiv.find('input[name=polling_mevcut]').prop('checked', true);
        }
        if (certificate_mevcut == "1") {
            modalDiv.find('input[name=certificate_mevcut]').prop('checked', true);
        }
        if (survey_mevcut == "1") {
            modalDiv.find('input[name=survey_mevcut]').prop('checked', true);
        }
        if (dry_run_required == "1") {
            modalDiv.find('input[name=dry_run_required]').prop('checked', true);
        }
        if (login_time_required == "1") {
            modalDiv.find('input[name=login_time_required]').prop('checked', true);
        }
        if (em_asia_report_required == "1") {
            modalDiv.find('input[name=em_asia_report_required]').prop('checked', true);
        }
        if (au_nz_report_required == "1") {
            modalDiv.find('input[name=au_nz_report_required]').prop('checked', true);
        }
        if (register_report_required == "1") {
            modalDiv.find('input[name=register_report_required]').prop('checked', true);
        }
        if (session_time_fixed == "1") {
            modalDiv.find('input[name=session_time_fixed]').prop('checked', true);
        }
        if (green_curtain == "1") {
            modalDiv.find('input[name=green_curtain]').prop('checked', true);
        }
        if (teleprompter == "1") {
            modalDiv.find('input[name=teleprompter]').prop('checked', true);
        }
        if (audience == "1") {
            modalDiv.find('input[name=audience]').prop('checked', true);
        }
        if (test_required == "1") {
            modalDiv.find('input[name=test_gerekli_check]').prop('checked', true);
        }else{
            modalDiv.find('input[name=test_gerekli_check]').prop('checked', false);
        }
        if (response.session.session_type == 'Dry-Run'){
            $("#duplicateSessionModal").find("#test_gerekli_div").removeClass("hide");
        }else{
            $("#duplicateSessionModal").find("#test_gerekli_div").addClass("hide");
        }

        if(response.session.servis_turu == "Destek"){
            modalDiv.find("#servis_turu_check").removeClass("hide");
        }
        if(response.session.session_type == "Online"){
            modalDiv.find("#yayin_platformu_div").removeClass("hide");
        }

        if (yayin_oncesi == "1") {
            modalDiv.find('input[name=yayin_oncesi]').prop('checked', true);
        }
        if (yayin_esnasinda == "1") {
            modalDiv.find('input[name=yayin_esnasinda]').prop('checked', true);
        }
        /*else{
            modalDiv.find('input[name=yayin_oncesi]').prop('checked', false);
            if (yayin_esnasinda != "1") {
                $(".servis_turu_check").addClass("hide");
            }
        }
        else{
            modalDiv.find('input[name=yayin_esnasinda]').prop('checked', false);
            if (yayin_oncesi != "1") {
                $(".servis_turu_check").addClass("hide");
            }
        }*/

        if (survey_completed_date != null) {
            modalDiv.find('input[name=survey_completed_date]').prop('checked', true);
        }

        if (satisfaction_survey != null) {
            modalDiv.find('#survey_sent_date').append('<i class="fa fa-thumbs-up" style="color: #0B792F;"></i> <strong>Memnuniyet Anketi Gönderildi</strong>');
            modalDiv.find('#checkboxDefault15').attr('disabled', false);

        }else {
            modalDiv.find('#survey_sent_date').append('<i class="fa fa-thumbs-down" style="color: #dc3519;"></i> <strong>Memnuniyet Anketi Gönderilmedi</strong>');
            modalDiv.find('#checkboxDefault15').attr('disabled', true);
        }

        if (report_status == "1") {
            modalDiv.find('input[name=report_status]').prop('checked', true);
        }
        $.timepicker.datetimeRange(
            $('#duplicateSessionModal .datetimepicker1duplicate'),
            $('#duplicateSessionModal .datetimepicker2duplicate'),
            {
                minInterval: (1000 * 60 * 30), // 1hr
                dateFormat: "dd/mm/yy",
                timeFormat: "HH:mm",
                defaultDate: "+1w",
                changeYear: true,
                changeMonth: true,
                numberOfMonths: 1,
                timeText: 'Zaman',
                hourText: 'Saat',
                minuteText: 'Dakika',
                start: {}, // start picker options
                end: {}, // end picker options
                nextText: 'İleri',
                prevText: 'Geri',
            }
        );
        modalDiv.modal();
    }).done(function () {
        $.timepicker.datetimeRange(
            $('#duplicateSessionModal .datetimepicker1duplicate'),
            $('#duplicateSessionModal .datetimepicker2duplicate'),
            {
                minInterval: (1000 * 60 * 30), // 1hr
                dateFormat: "dd/mm/yy",
                timeFormat: "HH:mm",
                defaultDate: "+1w",
                changeYear: true,
                changeMonth: true,
                numberOfMonths: 1,
                timeText: 'Zaman',
                hourText: 'Saat',
                minuteText: 'Dakika',
                start: {}, // start picker options
                end: {}, // end picker options
                nextText: 'İleri',
                prevText: 'Geri',
            }
        );
        message.close();
    })
        .fail(function (jqXHR, textStatus, errorThrown) {
            if (jqXHR.status != 401) {
                swal({
                    title: 'Bir Sorun Oluştu',
                    text: 'Yolunda Gitmeyen Birşeyler Var   Lütfen Yöneticiye Haber Veriniz ',
                    type: 'error'
                });
            }
        })
        .always(function () {
        });
}
//Yayın Çoğaltma Ajax Formu
$('#sessionDuplicateForm').submit(function (e) {

    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.
    keyboard:false;
    $(this).find('em').text('');

    if(document.getElementById("servis_turu").value == "Destek"){
        if(!document.getElementById('yayin_oncesi').checked && !document.getElementById('yayin_esnasinda').checked){
            swal({
                title: 'Dikkat!',
                text: 'Lütfen yayın öncesi veya yayın sırası destek seçeneklerinden birini seçiniz.',
                type: 'warning'
            });
            return;
        }
    }

    message.wait();
    // return false;
    $.ajax({
        url: "/session/duplicate",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Yayın Çoğaltıldı',
                type: 'success'
            }).then(function () {
                window.location.reload();
            })
        },
        error: function (res) {
            var response = res.responseJSON;
            var fields = [
                'title',
                'session_date',
                'start_date',
                'end_date',
                'cities_id',
                'location',
                'url',
                'main_language',
                'subject',
                'yayin_lokasyonu',
                'project_manager',
                'host_type',
                'host_country',
                'test_user',
                'room_number',
                'servis_turu',
                'webinar_cluster',
                'brand',
                'business_owner_name',
                'business_owner_email',
                'extra_field_2' // clickup_task_url
            ];
            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=edit" + fields[i] + "]").text(response[fields[i]][0]);
            }

            message.close();

            swal({
                title: 'Dikkat!',
                text: 'Formda doldurmanız gereken alanlar mevcut. Lütfen yeniden kontrol ediniz.',
                type: 'warning'
            });
        }
    });
});
$('.project_manager_duplicate').multiselect({
    buttonClass: 'multiselect dropdown-toggle btn btn-default btn-success',
    enableFiltering: true
});
$('.test_user_duplicate').multiselect({
    buttonClass: 'multiselect dropdown-toggle btn btn-default btn-success',
    enableFiltering: true
});
$('.multiselectTercumeEditDuplicate').multiselect({
    buttonClass: 'multiselect dropdown-toggle btn btn-default btn-success',
    enableFiltering: true
});

//Yayın Silme İşlemi
function sessionDelete(clickedsession_name) {
    swal({
        title: 'Silmek İstiyormusunuz',
        text: 'Yayın Silinecek',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ff0000',
        cancelButtonColor: '#00afff',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal Et'
    }).then(function () {
        $.ajax({
            url: "/session/sessionDelete/" + clickedsession_name,
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function (response) {
                swal({
                    title: 'Yayın Silindi',
                    text: 'Silme işlemi Tamamlandı',
                    type: 'success'
                })
                window.location.reload();
            },
            error: function (res) {
                swal({
                    title: 'İşlem Yapılamadı',
                    text: 'Yayın Silinirken Hata Oluştu',
                    type: 'error'
                })
            }

        });

    })

}


////////////////////////////////////////////  SESSİON   /////////////////////////////////////////////////////////////////

////////////////////////////////////////////  LOCATION    /////////////////////////////////////////////////////////////////

//Location Ekleme Ajax Formu
$('#locationAddForm').submit(function (e) {
    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.
    message.wait();
    $.ajax({
        url: "/location/locationAddForm",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Lokasyon Eklendi',
                type: 'success'
            }).then(function () {
                window.location.reload();
            })
        },

        error: function (res) {
            var response = res.responseJSON;
            var fields = [
                'country',
            ];
            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=new" + fields[i] + "]").text(response[fields[i]][0]);
            }
            message.close();
        }
    });
});
//
// $.ajaxSetup({
//     headers: {
//         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//     }
// });


//        Location Edit Ajax Formu

$('#locationEditForm').submit(function (e) {
    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.
    message.wait();
    $.ajax({
        url: "/location/locationEditForm",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Lokasyon Güncellendi',
                type: 'success'
            }).then(function () {
                window.location.reload();
            })
        },

        error: function (res) {
            var response = res.responseJSON;
            var fields = [
                'country',
            ];
            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=new" + fields[i] + "]").text(response[fields[i]][0]);
            }
            message.close();
        }
    });
});

// $.ajaxSetup({
//     headers: {
//         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//     }
// });


//Location Edit Form
function getLocationEdit(location_id) {
    $.getJSON('/location/show/' + location_id, function (response) {

        var modalDiv = $('#editLocation').modal({
            backdrop: 'static',
            keyboard: false
        });

        $('#editLocation').on('shown.bs.modal', function (e) {
            modalDiv.find('select[name=country]').val(response.country.id).change();
            modalDiv.find('textarea[name=location]').val(response.location.name);
            modalDiv.find('input[name=location_name]').val(response.location.location_name);
            modalDiv.find('input[name=location_channel]').val(response.location.location_channel);
            modalDiv.find('input[name=id]').val(response.location.id);
            modalDiv.find('select[name=cities_id]').val(response.location.cities_id);
            modalDiv.find('input[name=sessions_id]').val(response.location.sessions_id);
            modalDiv.find('button[id=deleteLocationID]').val(response.location.id);
            selected_city_id = response.location.cities_id;
        });

        //Yayın Ekibi
        var users = [];
        response.users.forEach(function (le) {
            users.push(le.id)
        });


        $("#multiSelectLocationEdit").val(users);
        $("#multiSelectLocationEdit").multiselect("refresh");

        modalDiv.find('.selected_speakers').html('');
        response.speakers.forEach(function (le) {
            modalDiv.find('.selected_speakers').prepend(
                '<span class="speaker_name">' +
                le.name +
                '&nbsp;&nbsp;<i class="fa fa-remove" style="color: red;cursor: pointer" onclick="$(this).parent().remove()"></i>' +
                '</span>'
            );
        });
        modalDiv.modal();
    }).done(function () {

    })
        .fail(function () {
            swal({
                title: 'Bir Sorun Oluştu',
                text: 'Yolunda Gitmeyen Birşeyler Var   Lütfen Yöneticiye Haber Veriniz ',
                type: 'error'
            });
        })
        .always(function () {

        });

}

//Locasyon İçin ID Alımı Modal Doldurma
function getSessionLocation(session_id) {
    $.getJSON('/session/show/' + session_id, function (response) {

        var modalDiv = $('#addLocation').modal({
            backdrop: 'static',
            keyboard: false
        });
        modalDiv.find('input[name=id]').val(response.session.id);
        modalDiv.modal();
    });
}


//Lokasyon Silme İşlemi
function locationDelete(clickedlocation_name) {
    swal({
        title: 'Silmek İstiyormusunuz',
        text: 'Yayın Silinecek',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ff0000',
        cancelButtonColor: '#00afff',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal Et'
    }).then(function () {
        $.ajax({
            url: "/location/locationDelete/" + clickedlocation_name,
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function (response) {
                swal({
                    title: 'Lokasyon Silindi',
                    text: 'Silme işlemi Tamamlandı',
                    type: 'success'
                })
                window.location.reload();
            },
            error: function (res) {
                swal({
                    title: 'İşlem Yapılamadı',
                    text: 'Lokasyon Silinirken Hata Oluştu',
                    type: 'error'
                })
            }

        });

    })

}


////////////////////////////////////////////     LOCATION  /////////////////////////////////////////////////////////////////

////////////////////////////////////////////     EXPENS   /////////////////////////////////////////////////////////////////
//Masraf Ekleme Formu
$('#expenseSave').submit(function (e) {
    e.preventDefault(); //default olarak form değerlerini devre dışı bırak aşağıdaki işlemi çalıştır.
    $.ajax({
        url: "/expense/save",
        type: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Masraf Eklendi',
                type: 'success'
            }).then(function () {
                window.location.reload();
            })
        },

        error: function (res) {
            var response = res.responseJSON;
            var fields = [
                'amount',
                'name',
            ];

            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=new" + fields[i] + "]").text(response[fields[i]][0]);
            }
            console.log(response);

            var error_messages = '';
            var tur = '';
            for (var i in response) {

                var rowID = parseInt(i.split('.')[0]) + 1;
                if (i.split('.')[1] === "amount") {
                    tur = 'Masraf Tutarı';
                } else {
                    tur = 'Masraf Açıklama'
                }
                error_messages += rowID + '. Satırdaki ' + tur + ' ' + response[i][0] + '\n';
            }
            swal(
                '',
                error_messages,
                'error'
            );
        }
    });
});


//Masraf Formu İşlemleri
function getExpense(sessions_id) {
    $.ajax({
        type: "GET",
        url: "/expense/show/" + sessions_id,
        dataType: "json",
        success: function (response) {

            var modalDiv = $('#newExpense').modal({
                backdrop: 'static',
                keyboard: false,
            });

            drawTable(response.expense);

            modalDiv.find('input[name=sessions_id]').val(response.sessions.id);
            modalDiv.modal();
        },
        error: function (jqXHR, textStatus, errorThrown) {
            alert("loading error data " + errorThrown);
        }
    });
}

////////////////////////////////////////////     EXPENSE   /////////////////////////////////////////////////////////////////


////////////////////////////////////////////     ORDERS   /////////////////////////////////////////////////////////////////

//Yeni Teklif Ekleme Formu
$('#orderAddForm').submit(function (e) {

    $('#orderAddForm input[name=invoice_status]')
        .attr("disabled", true);

    $('#fdurum')
        .attr('disabled', false)
        .attr('name', 'invoice_status');

    //disable the default form submission
    event.preventDefault();

    //grab all form data
    var formData = new FormData($(this)[0]);

    formData.append('invoice_count', $(this).find('.invoice_row').length);
    $(this).find('em').text('');
     message.wait();
    $.ajax({
        url: "/order/save",
        type: 'POST',
        data: formData,
        async: false,
        cache: false,
        contentType: false,
        processData: false,
        dataType: 'json',
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Teklif Eklendi',
                type: 'success',
                onOpen: function () {
                    swal.hideLoading();
                }
            }).then(function () {
                window.location.reload();
            })
        },
        error: function (res) {
            var response = res.responseJSON;
            if (typeof response.invoice_status_required !== 'undefined') {
                swal('Hata', response.invoice_status_required, 'error');
            }
            var fields = [
                'name',
                'request_date',
                'order_amount',
                'sent_date',
                'status',
                'invoice_status',
                'neden_value'
            ];

            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0]) {
                    $("em[for=new" + fields[i] + "]").text(response[fields[i]][0]);
                }
            }

            var invoice_fields = [
                'invoice_amount',
                'invoice_currency',
                'invoice_number',
                'invoice_date',
                'invoice_note'
            ];

            for (var i in response) {
                var field_data = i.split('.');
                var field_name = field_data[0];
                var field_index = field_data[1];

                if (field_name && field_index) {
                    $('.invoice_row').eq(field_index).parent().find('em.' + field_name + '_error').text(response[i][0]);
                }
            }
            message.close();

        }
    });
    return false;
});
//
// $.ajaxSetup({
//     headers: {
//         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//     }
// });


//faturalandı combobox için değişen textboxlar

// Order Add İçindeki Faturalama Ekranı İçin Dynamic Textboxlar
var room = 1;

function newinvoicefield() {

    room++;
    var objTo = document.getElementById('newinvoicefield')
    var divtest = document.createElement("div");
    divtest.setAttribute("class", "form-group removeclass" + room);
    divtest.setAttribute('style', 'display: flex');
    var rdiv = 'removeclass' + room;
    $('.newinvoicedate').mask('00/00/0000');
    divtest.innerHTML = '<div class="col-sm-2 nopadding invoice_row">\n' +
        '                                <div class="form-group">\n' +
        '                                    <label for="">Fatura Tarihi</label>\n' +
        '                                    <input type="text" class="form-control invoice_date newinvoicedate"  name="invoice_date[]" value="" ">\n' +
        '                                   <div class="calendar_icon" style="position: absolute;top: 34px;right: 25px;"><i class="fa fa-calendar-o"></i></div>\n' +
        '                                    <em for="" class="invoice_date_error" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        '                            <div class="col-sm-3 nopadding">\n' +
        '                                <div class="form-group">\n' +
        '                                    <label for="">Fatura Tutarı</label>\n' +
        '                                    <input type="text" step="0.01" class="form-control invoice_amount sayikontrol4"  name="invoice_amount[]" value="" >\n' +
        '                                    <em for="" class="invoice_amount_error" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        '                            <div class="col-sm-2 nopadding">\n' +
        '                                <div class="form-group"><label for="">Döviz Cinsi</label>\n' +
        '                                    <select name="invoice_currency[]" class="form-control">\n' +
        '                                        <option value="">Döviz</option>\n' +
        '                                        <option value="TL">TL</option>\n' +
        '                                        <option value="USD">USD</option>\n' +
        '                                        <option value="EU">EU</option>\n' +
        '                                        <option value="GBP">GBP</option>\n' +
        '                                    </select>\n' +
        '                                    <em for="" class="invoice_currency_error" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        '                            <div class="col-sm-2 nopadding">\n' +
        '                                <div class="form-group">\n' +
        '                                    <label for="">Fatura Numarası</label>\n' +
        '                                    <input type="text" class="form-control"   name="invoice_number[]" value="" >\n' +
        '                                    <em for="" class="invoice_number_error" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        ' \n' +
        '                            <div class="col-sm-3 nopadding">\n' +
        '                                <div class="form-group">\n' +
        '                                    <div class="input-group">\n' +
        '                                        <label for="">Fatura Açıklaması</label>\n' +
        '                                        <input name="invoice_note[]" id="readonlyy"  class="form-control" >' +
        '<div class="input-group-btn"  style="padding-top: 24px;">' +
        ' <button class="btn btn-danger" type="button" onclick="remove_newinvoicefield(' + room + ');"> ' +
        '<span class="glyphicon glyphicon-minus" aria-hidden="true"></span>' +
        ' </button></div>' +
        '</div><em for="" class="invoice_note_error" style="color: red;"></em></div></div><div class="clear"></div>';

    objTo.appendChild(divtest)
}

function remove_newinvoicefield(rid) {
    $('.removeclass' + rid).remove();
}


$(document).ready(function () {
    $("#tdurum").on("change", function () { //Teklif Durum değiştiğinde
        var x = $(this).val();
        if ($(this).val() === "2") {
            $('[id=fdurum]').prop('disabled', false);
            $('[name=po_number]').prop('readonly', false);
            $('[name=po_location]').prop('readonly', false);

        }
        else {
            $('[id=fdurum]').prop('disabled', true);
            $('[name=po_number]').prop('readonly', true);
            $('[name=po_location]').prop('readonly', true);


        }
    });

    $("#fdurum").on("change", function () {
        var x = $(this).val();
        if ($(this).val() === "1") {
            $('[name=po_number]').prop('readonly', false);
            $('[name=po_location]').prop('readonly', false);
        } else if ($(this).val() === "2") {
            $('[name=po_number]').prop('readonly', true);
            $('[name=po_location]').prop('readonly', true);
        }
        else if ($(this).val() === "0") {
            $('[name=po_number]').prop('readonly', false);
            $('[name=po_location]').prop('readonly', false);

        }
    });

    $("#tdurumedit").on("change", function () {
        var m = $(this).val();
        if ($(this).val() == "2") {

            $('[name=invoice_status]').prop('disabled', false);
            $('[name=po_number]').prop('readonly', false);
            $('[name=po_location]').prop('readonly', false);

            console.log('İçeri Girdi');
        }
        else {
            $('[name=invoice_status]').prop('disabled', true);
            $('[name=po_number]').prop('readonly', true);
            $('[name=po_location]').prop('readonly', true);


        }
    });


    $("#fdurumedit").on("change", function () {



        var x = $(this).val();
        if ($(this).val() === "1") {
            $('[name=po_number]').prop('readonly', false);
            $('[name=po_location]').prop('readonly', false);

        } else if ($(this).val() === "2") {
            $('[name=po_number]').prop('readonly', true);
            $('[name=po_location]').prop('readonly', true);

        }

        else if ($(this).val() === "0") {
            $('[name=po_number]').prop('readonly', false);
            $('[name=po_location]').prop('readonly', false);
        }
    });
});


//Teklif Ekranı Düzenleme Doldurma
function getOrderInfo(order_id) {
    $.getJSON('/order/now/' + order_id, function (response) {

        var modalDiv = $('#editOrderForm').modal({
            backdrop: 'static',
            keyboard: false
        });

        modalDiv.find('input[name=name]').val(response.order.name);
        modalDiv.find('input[name=video_edit_update_id]').val(response.order.video_edit_id);
        modalDiv.find('input[name=request_date]').val(response.order.request_date);
        modalDiv.find('input[name=order_amount]').val(response.order.order_amount);
        modalDiv.find('select[name=currency]').val(response.order.currency);
        modalDiv.find('input[name=sent_date]').val(response.order.sent_date);
        modalDiv.find('input[name=email]').val(response.order.email);
        modalDiv.find('select[name=status]').val(response.order.status);
        modalDiv.find('textarea[name=note]').val(response.order.note);
        modalDiv.find('input[name=po_number]').val(response.order.po_number);
        modalDiv.find('input[name=po_location]').val(response.order.po_location);
        modalDiv.find('select[name=invoice_status]').val(response.order.invoice_status).change();
        modalDiv.find('input[name=id]').val(response.order.id);
        modalDiv.find('button[id=deleteID]').val(response.order.id);

        function remove_invoiceeditrow(rid) {
            $('.removeclass' + rid).remove();
        }

        function remove_old_invoice(t) {

        }

        //Redddedildi ise görünürlük açılıyor.
        if(response.order.status == 3){
            $('#edit_red_neden, #edit_diger_block').removeClass('hidden');
        }

        var invoices = [];
        // var d=new Date(date);
        response.invoices.forEach(function (invs) {
            var objTo = document.getElementById('invoiceeditrow')
            var divtestedite = document.createElement("div");
            divtestedite.setAttribute("class", "form-group removeclass");
            var rdiv = 'removeclass';
            var tl_selected = '';
            var usd_selected = '';
            var eu_selected = '';
            var gbp_selected = '';

            if (invs.currency == 'TL') {
                tl_selected = 'selected';
            }
            if (invs.currency == 'USD') {
                usd_selected = 'selected';
            }
            if (invs.currency == 'EU') {
                eu_selected = 'selected';
            }
            if (invs.currency == 'GBP') {
                gbp_selected = 'selected';
            }
            $('.editinvoicedate').mask('00/00/0000');

            divtestedite.innerHTML = '<div class="col-sm-2 nopadding invoice_rowedit">\n' +
                '                                <input name="invoice_id[]" type="hidden" value="' + invs.id + '"> \n ' +
                '                                <div class="form-group">\n' +
                '                                    <label for="">Fatura Tarihi</label>\n' +
                '                                    <input type="text" class="form-control invoice_date editinvoicedate"  name="invoice_date[]" value="' + invs.date + '"  >\n' +
                '                                  <div class="calendar_icon" style="position: absolute;top: 34px;right: 25px;"><i class="fa fa-calendar-o"></i></div>\n' +
                '                                   <em for="" class="invoice_date_erroredit" style="color: red;"></em>' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                            <div class="col-sm-3 nopadding">\n' +
                '                                <div class="form-group">\n' +
                '                                    <label for="">Fatura Tutarı</label>\n' +
                '                                    <input type="text" step="0.01" class="form-control sayikontrol4"  name="invoice_amount[]" value="' + invs.amount + '"  >\n' +
                '                                   <em for="" class="invoice_amount_erroredit" style="color: red;"></em>' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                            <div class="col-sm-2 nopadding">\n' +
                '                                <div class="form-group"><label for="">Döviz Cinsi</label>\n' +
                '                                    <select name="invoice_currency[]" class="form-control">\n' +
                '                                        <option value="">Döviz</option>\n' +
                '                                        <option value="TL" ' + tl_selected + '>TL</option>\n' +
                '                                        <option value="USD" ' + usd_selected + '>USD</option>\n' +
                '                                        <option value="EU" ' + eu_selected + '>EU</option>\n' +
                '                                        <option value="GBP" ' + gbp_selected + '>GBP</option>\n' +
                '                                    </select>\n' +
                '                                   <em for="" class="invoice_currency_erroredit" style="color: red;"></em>' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                            <div class="col-sm-2 nopadding">\n' +
                '                                <div class="form-group">\n' +
                '                                    <label for="">Fatura Numarası</label>\n' +
                '                                    <input type="text" class="form-control"   name="invoice_number[]" value="' + invs.number + '" >\n' +
                '                                   <em for="" class="invoice_number_erroredit" style="color: red;"></em>' +
                '                                </div>\n' +
                '                            </div>\n' +
                ' \n' +
                '                            <div class="col-sm-3 nopadding">\n' +
                '                                <div class="form-group">\n' +
                '                                    <div class="input-group">\n' +
                '                                        <label for="">Fatura Açıklaması</label>\n' +
                '                                        <input name="invoice_note[]" id="readonlyy"  class="form-control" value="' + invs.note + '" ><div class="input-group-btn"  style="padding-top: 24px;"> ' +
                '<button class="btn btn-danger" type="button" onclick="$(this).closest(\'.removeclass\').remove();"> ' +
                '<span class="glyphicon glyphicon-minus" aria-hidden="true"></span> </button></div></div>' +
                '<em for="" class="invoice_note_erroredit" style="color: red;"></em>' +
                '</div></div><div class="clear"></div>';

            objTo.appendChild(divtestedite);

        });

        if (response.order.invoice_status === "1") {
            $('[name=invoice_currency]').prop('disabled', false);
            $('[name=po_number]').prop('readonly', false);
            $('[name=po_location]').prop('readonly', false);
        }

        if (response.order.status != "2") {
            $('[name=invoice_status]').prop('disabled', true);
            $('[name=po_number]').prop('readonly', true);
            $('[name=po_location]').prop('readonly', true);
        }
        //Yayınlar
        var sessions = [];
        response.sessions.forEach(function (ler) {
            sessions.push(ler.id)
        });
        $("#multiSelectYayinEdit").val(sessions);
        $("#multiSelectYayinEdit").multiselect("refresh");

        modalDiv.modal();

    }).done(function () {

    })
        .fail(function () {
            swal({
                title: 'Bir Sorun Oluştu',
                text: 'Yolunda Gitmeyen Birşeyler Var   Lütfen Yöneticiye Haber Veriniz ',
                type: 'error'
            });
        })
        .always(function () {
        });
}

// FAturalar Edit BÖlümü
var roomedit = 1;

function editnewinvoicerow() {
    roomedit++;
    var objTo = document.getElementById('editnewinvoicerow')
    var divtestedit = document.createElement("div");
    divtestedit.setAttribute("class", "form-group removeclass" + roomedit);
    var rdiv = 'removeclass' + roomedit;
    $('.editnewfieldinvoice').mask('00/00/0000');
    divtestedit.innerHTML = '<div class="col-sm-2 nopadding invoice_rowedit">\n' +
        '                                <div class="form-group">\n' +
        '                                <input name="invoice_id[]" type="hidden" value=""> \n ' +
        '                                    <label for="">Fatura Tarihi</label>\n' +
        '                                    <input type="text" class="form-control invoice_date editnewfieldinvoice"  name="invoice_date[]" value=""  >\n' +
        '                                  <div class="calendar_icon" style="position: absolute;top: 34px;right: 25px;"><i class="fa fa-calendar-o"></i></div>\n' +
        '                                   <em for="" class="invoice_date_erroredit" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        '                            <div class="col-sm-3 nopadding">\n' +
        '                                <div class="form-group">\n' +
        '                                    <label for="">Fatura Tutarı</label>\n' +
        '                                    <input type="text" step="0.01" class="form-control sayikontrol4"  name="invoice_amount[]" value="" >\n' +
        '                                    <em for="" class="invoice_amount_erroredit" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        '                            <div class="col-sm-2 nopadding">\n' +
        '                                <div class="form-group"><label for="">Döviz Cinsi</label>\n' +
        '                                    <select name="invoice_currency[]" class="form-control">\n' +
        '                                        <option value="">Döviz</option>\n' +
        '                                        <option value="TL">TL</option>\n' +
        '                                        <option value="USD">USD</option>\n' +
        '                                        <option value="EU">EU</option>\n' +
        '                                        <option value="GBP">GBP</option>\n' +
        '                                    </select>\n' +
        '                                   <em for="" class="invoice_currency_erroredit" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        '                            <div class="col-sm-2 nopadding">\n' +
        '                                <div class="form-group">\n' +
        '                                    <label for="">Fatura Numarası</label>\n' +
        '                                    <input type="text" class="form-control "   name="invoice_number[]" value=""  >\n' +
        '                                   <em for="" class="invoice_number_erroredit" style="color: red;"></em>' +
        '                                </div>\n' +
        '                            </div>\n' +
        ' \n' +
        '                            <div class="col-sm-3 nopadding">\n' +
        '                                <div class="form-group">\n' +
        '                                    <div class="input-group">\n' +
        '                                        <label for="">Fatura Açıklaması</label>\n' +
        '                                        <input name="invoice_note[]" id="readonlyy"  class="form-control" ><div class="input-group-btn"  style="padding-top: 24px;"> <button class="btn btn-danger" type="button" onclick="remove_editnewinvoicerow(' + roomedit + ');"> <span class="glyphicon glyphicon-minus" aria-hidden="true"></span> </button></div></div>' +
        ' <em for="" class="invoice_note_erroredit" style="color: red;"></em></div></div><div class="clear"></div>';

    objTo.appendChild(divtestedit)
}

function remove_editnewinvoicerow(rid) {
    $('.removeclass' + rid).remove();
}


//Teklif Düzenleme Formu
$('#orderEditForm').submit(function (e) {

    //disable the default form submission
    event.preventDefault();
    $(this).find('em').text('');
    //grab all form data
    var formData = new FormData($(this)[0]);
    formData.append('invoice_count', $(this).find('.invoice_rowedit').length);

    $.ajax({
        url: "/order/orderEditForm",
        type: 'POST',
        data: formData,
        async: false,
        cache: false,
        contentType: false,
        dataType: 'json',
        processData: false,
        success: function (response) {
            swal({
                title: 'Tebrikler İşlem Başarılı',
                text: 'Teklif Güncellendi',
                type: 'success'
            }).then(function () {
                if (window.location.search !== "") {
                    window.location.replace('/order/waitingOrderList')
                } else {
                    window.location.reload();
                }
            })

        },
        error: function (res) {
            var response = res.responseJSON;
            if (typeof response.invoice_status_required !== 'undefined') {
                swal('Hata', response.invoice_status_required, 'error');
            }
            var fields = [
                'name',
                'request_date',
                'order_amount',
                'sent_date',
                'status',
                'neden_value'
            ];

            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0]) {
                    $("em[for=edit" + fields[i] + "]").text(response[fields[i]][0]);
                }
            }

            var invoice_fields = [
                'invoice_amount',
                'invoice_currency',
                'invoice_number',
                'invoice_date',
                'invoice_note'
            ];

            for (var i in response) {
                var field_data = i.split('.');
                var field_name = field_data[0];
                var field_index = field_data[1];

                if (field_name && field_index) {
                    $('.invoice_rowedit').eq(field_index).parent().find('em.' + field_name + '_erroredit').text(response[i][0]);
                }
            }

        }
    });
    return false;
});

$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});


function orderDelete(clicked_name) {
    swal({
        title: 'Silmek İstiyormusunuz',
        text: 'Teklif Silinecek',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ff0000',
        cancelButtonColor: '#00afff',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal Et'
    }).then(function () {
        $.ajax({
            url: "/order/orderDelete/" + clicked_name,
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function (response) {
                swal({
                    title: 'Teklif Silindi',
                    text: 'Silme işlemi Tamamlandı',
                    type: 'success'
                })
                window.location.reload();
            },
            error: function (res) {
                swal({
                    title: 'İşlem Yapılamadı',
                    text: 'Teklif Silinirken Hata Oluştu',
                    type: 'error'
                })
            }
        });
    })
}


////////////////////////////////////////////     ORDERS   /////////////////////////////////////////////////////////////////


////////////////////////////////////////////     FILES   /////////////////////////////////////////////////////////////////

//File Upload İçin Yüklenen Ekran
function getFileInfo(file_id) {
    var modalDiv = $('#fileUploadForm').modal({
        backdrop: 'static',
        keyboard: false
    });

    $.ajax({
        url: '/order/show/' + file_id,
        type: 'GET',
        success: function (response) {
            $('#divid').html(response);
            modalDiv.find('input[name=orders_id] ').val(file_id);
            modalDiv.modal();
        },
        error: function (res) {
        }
    });

}

//Order File Silme Ekranı
function deleteImg(img_id, t) {
    swal({
        title: 'Emin misiniz?',
        text: 'Dosyayı Silmek İstediğinizden Emin misiniz?',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ff0000',
        cancelButtonColor: '#00afff',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal Et'
    }).then(function () {
        $.ajax({
            url: "/order/deleteImage/" + img_id,
            type: 'GET',
            data: $(this).serialize(),
            dataType: 'json',
            success: function (response) {
                swal({
                    title: 'Dosya Silindi',
                    text: 'Silme işlemi Tamamlandı',
                    type: 'success'
                });
                if (t) {
                    $(t).closest('.dz-preview').remove();
                }

            },
            error: function (response) {
                swal({
                    title: 'İşlem Yapılamadı',
                    text: 'Dosya Silinirken Hata Oluştu',
                    type: 'error'
                })
            }

        });

    })
}

function formatAsMoney(mnt) {
    mnt -= 0;
    mnt = (Math.round(mnt * 100)) / 100;
    return (mnt == Math.floor(mnt)) ? mnt + '.00'
        : ((mnt * 10 == Math.floor(mnt * 10)) ?
            mnt + '0' : ((mnt * 100 == Math.floor(mnt * 100)) ? mnt + '0' : mnt));
}


/* LOKASYON TAB'I FONKSİYONLARI */
function addNewLocationPopup() {
    var fields = [
        'country', 'users_id'
    ];
    for (var i in fields) {
        $("em[for=new-" + fields[i] + "]").text('');
    }
    var stat = false;
    if($('#newLocationPopup select[name=country]').val() === ''){
        stat = true;
        $("em[for=new-country]").text('Bu alan zorunludur.');
    }
    if (!$('select#multiSelectLocationEdit').val()){
        stat = true;
        $("em[for=new-users_id]").text('Bu alan zorunludur.');
    }
    if (stat){
        return null;
    }

    $('#newLocationPopup input[name="speakers[]"]').remove();
    $('#newLocationPopup .selected_speakers span').each(function () {
        var speaker = $.trim($(this).text());
        $('#newLocationPopup').append('<input type="hidden" name="speakers[]" value="' + speaker + '">'
        )
    });
    speakerList = [];
    $('#newLocationPopup input[name="speakers[]"]').each(function(){
        speakerList.push($(this).val());
    });
    var data = {};
    data['_token'] = $('input[name="_token"]').val();
    data['id'] = $('input[name=id]').val();
    data['country'] = $('#newLocationPopup select[name=country]').val();
    data['cities_id'] = $('#newLocationPopup select[name=cities_id]').val();
    data['location_channel'] = $('#newLocationPopup input[name=location_channel]').val();
    data['location_name'] = $('#newLocationPopup input[name=location_name]').val();
    data['location'] = $('#newLocationPopup textarea[name=location]').val();
    data['users_id[]'] = $('select#multiSelectLocationEdit').val();
    data['speakers[]'] = speakerList;
    message.wait();
    $.ajax({
        url: '/location/locationAddForm',
        type: 'POST',
        data: data,
        success: function(result){
            $('#newLocationPopup').hide();
            var userListText = [];
            $('select#multiSelectLocationEdit').find('option:selected').each(function(){
                userListText.push($(this).text());
            });
            makeNewLocationRow(
                $('#newLocationPopup select[name=country] option:selected').text(),
                $('#newLocationPopup select[name=cities_id] option:selected').text(),
                data.location_name,
                data.location,
                userListText.join(', '),
                data['speakers[]'].join(', '),
                result.result.location_id,
                data.country,
                data.cities_id,
                data.location_channel
            );
            $('#newLocationPopup select[name=country]').val('');
            $('#newLocationPopup select[name=cities_id]').val('');
            $('#newLocationPopup .selected_speakers').html('');
            $('#newLocationPopup input[name="speakers[]"]').remove();
            $('select#multiSelectLocationEdit').multiselect('refresh');
            $("select#multiSelectLocationEdit option:selected").prop("selected", false);
            $('select#multiSelectLocationEdit').multiselect('refresh');
            $('#newLocationPopup input[name=location_name]').val('');
            $('#newLocationPopup textarea[name=location]').val('');
            $('#newLocationPopup input[name=location_channel]').val('');
            message.close();
        },
        error: function(res){
            var response = res.responseJSON;
            var fields = [
                'country', 'users_id'
            ];
            for (var i in fields) {
                $("em[for=new-" + fields[i] + "]").text('');
            }
            for (var i in fields) {
                if (response[fields[i]] && response[fields[i]][0])
                    $("em[for=new-" + fields[i] + "]").text(response[fields[i]][0]);
            }
            message.close();
        }
    });
}
function editNewLocationPopup(){
    $('#newLocationPopup input[name="speakers[]"]').remove();
    $('#newLocationPopup .selected_speakers span').each(function () {
        var speaker = $.trim($(this).text());
        $('#newLocationPopup').append('<input type="hidden" name="speakers[]" value="' + speaker + '">'
        )
    });
    speakerList = [];
    $('#newLocationPopup input[name="speakers[]"]').each(function(){
        speakerList.push($(this).val());
    });
    var data = {};
    data['_token'] = $('input[name="_token"]').val();
    data['id'] = $('#newLocationPopup input[name=location-id]').val();
    data['sessions_id'] = $('#newLocationPopup input[name=session-id]').val();
    data['country'] = $('#newLocationPopup select[name=country]').val();
    data['cities_id'] = $('#newLocationPopup select[name=cities_id]').val();
    data['location_channel'] = $('#newLocationPopup input[name=location_channel]').val();
    data['location_name'] = $('#newLocationPopup input[name=location_name]').val();
    data['location'] = $('#newLocationPopup textarea[name=location]').val();
    data['users_id[]'] = $('select#multiSelectLocationEdit').val();
    data['speakers[]'] = speakerList;

    var fields = [
        'country', 'users_id'
    ];
    for (var i in fields) {
        $("em[for=new-" + fields[i] + "]").text('');
    }
    var stat = false;
    if($('#newLocationPopup select[name=country]').val() === ''){
        stat = true;
        $("em[for=new-country]").text('Bu alan zorunludur.');
    }
    if (!$('select#multiSelectLocationEdit').val()){
        stat = true;
        $("em[for=new-users_id]").text('Bu alan zorunludur.');
    }
    if (stat){
        return null;
    }

    message.wait();
    $.ajax({
        url: '/location/locationEditForm',
        type: 'POST',
        data: data,
        success: function(result){
            $('table #locationRow' + data.id).remove();
            var userListText = [];
            $('select#multiSelectLocationEdit').find('option:selected').each(function(){
                userListText.push($(this).text());
            });
            makeNewLocationRow(
                $('#newLocationPopup select[name=country] option:selected').text(),
                $('#newLocationPopup select[name=cities_id] option:selected').text(),
                data.location_name,
                data.location,
                userListText.join(', '),
                data['speakers[]'].join(', '),
                data.id,
                data.country,
                data.cities_id,
                data.location_channel
            );
            $('#newLocationPopup').hide();
            message.close();
        },
        error: function(){
            message.close();
        }
    });
}

function locationSaveModal(){
    $('#newLocationPopup .country_dropdown_popup').val('');
    $('#newLocationPopup input[name=location_name]').val('');
    $('#newLocationPopup textarea[name=location]').val('');
    $('.city_dropdown_popup').val('');
    $('#newLocationPopup').find('.selected_speakers').html('');
    $('#newLocationPopup #multiSelectLocationEdit').val('');
    $('#newLocationPopup #multiSelectLocationEdit').multiselect('destroy');
    $('#newLocationPopup #multiSelectLocationEdit').multiselect({
        buttonClass: 'multiselect dropdown-toggle btn btn-default btn-success',
        enableFiltering: true
    });

    $('#newLocationEditBtn').addClass('hide');
    $('#newLocationSaveBtn').removeClass('hide');
    $('#newLocationPopup').toggle();
}
function locationEditModal(value){
    var data = {};
    data['_token'] = $('input[name="_token"]').val();
    data['id'] = value;
    data['country'] = $('#locationRow' + value +' input[name=country_id]').val();
    data['cities_id'] = $('#locationRow' + value +' input[name=city_id]').val();
    data['location_name'] = $('#locationRow' + value +' input[name=location_name]').val();
    data['location_channel'] = $('#locationRow' + value +' input[name=location_channel]').val();
    data['location'] = $('#locationRow' + value +' input[name=description]').val();
    data['users_id'] = $('#locationRow' + value +' input[name=session_users]').val().split(',');
    data['speakers'] = $('#locationRow' + value +' input[name=speakers]').val().split(',');

    $('#newLocationPopup .country_dropdown_popup').val(data['country']);
    $('#newLocationPopup input[name=location_name]').val('');
    if (data['location_name'] != '-' && data['location_name'] != '_' && data['location_name'] != ''){
        $('#newLocationPopup input[name=location_name]').val(data['location_name']);
    }
    $('#newLocationPopup textarea[name=location]').val('');
    if (data['location'] != '-' && data['location'] != '_' && data['location'] != ''){
        $('#newLocationPopup textarea[name=location]').val(data['location']);
    }
    $('#newLocationPopup input[name=location_channel]').val('');
    if (data['location_channel'] != '-' && data['location_channel'] != '_' && data['location_channel'] != ''){
        $('#newLocationPopup input[name=location_channel]').val(data['location_channel']);
    }

    $.get('/ajax/countryGet/' + data['country'], function (response) {
        var city_options = '<option value="">Seçiniz</option>';
        for (var i in response){
            city_options += '<option value="' + response[i].id + '" >' + response[i].name + '</option>';
        }
        $('.city_dropdown_popup').html(city_options);
        $('.city_dropdown_popup').val(data['cities_id']);
    });

    $('#newLocationPopup').find('.selected_speakers').html('');

    data['speakers'].forEach(function(item){
        if (item != '-' && item != '_' && item != '' ) {
            addSpeakerPopup(item)
        }
    });
    $('#newLocationPopup #multiSelectLocationEdit').multiselect('destroy');
    $('#newLocationPopup #multiSelectLocationEdit').val('');
    data['users_id'].forEach(function(l){
        $("#newLocationPopup #multiSelectLocationEdit option").each(function(){
            if($(this).text() == l){
                $(this).prop("selected",true);
            }
        });
    })
    $('#newLocationPopup #multiSelectLocationEdit').multiselect({
        buttonClass: 'multiselect dropdown-toggle btn btn-default btn-success',
        enableFiltering: true
    });
    // $('#newLocationEditBtn').attr('data-session-id', $('#tab2_3 input[name=id]').val());
    $('#newLocationPopup input[name=session-id]').val($('#tab2_3 input[name=id]').val())
    // $('#newLocationEditBtn').attr('data-location-id', value);
    $('#newLocationPopup input[name=location-id]').val(value);
    $('#newLocationEditBtn').attr('data-location-id', value);
    $('#newLocationSaveBtn').addClass('hide');
    $('#newLocationEditBtn').removeClass('hide');
    $('#newLocationPopup').toggle();
}
function makeNewLocationRow(country, city, location_name, location, session_users, speakers, id, country_id, city_id, location_channel){
    $('#locationList tbody').append("<tr id='locationRow" + id + "'>" +
        "<td>" + country + "</td>" +
        "<td>" + city + "</td>" +
        "<td>" + speakers + "</td>" +
        "<td>" + session_users + "</td>" +
        "<td>" + location_name + "</td>" +
        "<td>" + location_channel + "</td>" +
        "<td>" + location + "</td>" +
        "<td>" +
            "<input type='hidden' name='country_id' value='" + country_id + "'>" +
            "<input type='hidden' name='city_id' value='" + city_id + "'>" +
            "<input type='hidden' name='speakers' value='" + speakers + "'>" +
            "<input type='hidden' name='session_users' value='" + session_users + "'>" +
            "<input type='hidden' name='location_channel' value='" + location_channel + "'>" +
            "<input type='hidden' name='location_name' value='" + location_name + "'>" +
            "<input type='hidden' name='description' value='" + location + "'>" +
            "<button id='editLocationID' class='btn btn-warning btn-sm' type='button' onclick='locationEditModal(this.value)' value='" + id + "'><i class='fa fa-edit'></i> Düzenle</button>" +
            "<button id='deleteLocationID' class='btn btn-danger btn-sm' type='button' onclick='locationDelete(this.value)' value='" + id + "'><i class='fa fa-trash'></i> Sil</button>" +
        "</td>" +
        "</tr>");
}

function select_test_user() {
    var options = document.getElementById('test_user').options, count = 0;
    for (var i=0; i < options.length; i++) {
        if (options[i].selected) count++;
    }
}

function select_servis_turu_duplicate() {
    var option_value = $('#sessionDuplicateForm #servis_turu').val();
    if(option_value == "Destek"){
        $("#sessionDuplicateForm #servis_turu_check").removeClass("hide");
    }else{
        $("#sessionDuplicateForm #servis_turu_check").addClass("hide");
    }
}
function select_session_type_duplicate() {
    var option_value = $('#sessionDuplicateForm #session_type_2').val();
    if(option_value == "Dry-Run"){
        $("#sessionDuplicateForm #test_gerekli_div").removeClass("hide");
    }else{
        $("#sessionDuplicateForm #test_gerekli_div").addClass("hide");
    }
    if(option_value == "Online"){
        $("#sessionDuplicateForm #yayin_platformu_div").removeClass("hide");
    }else{
        $("#sessionDuplicateForm #yayin_platformu_div").addClass("hide");
    }
}