.bootstrap-tagsinput {
  position: relative;
  vertical-align: top;
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  height: auto;
  min-height: 42px;
  outline: 0;
  padding: 3px 8px;
  margin: 0;

  cursor: text;
  color: #626262;
  font-size: 14px;
  font-weight: 400;

  border: 1px solid #E5E5E5;
  border-radius: 0;

  -webkit-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;

  &:focus,
  &:hover {
    border-color: #DDD;
    background-color: #FFF !important;
  }

  input {
    border: none;
    box-shadow: none;
    outline: none;
    background-color: transparent;
    padding: 0;
    margin: 0;
    width: auto !important;
    max-width: inherit;

    &:focus {
      border: none;
      box-shadow: none;
    }
  }

  .tag {
    color: white;
    margin-right: 5px;
    line-height: 36px;
    padding: .6em .7em .7em;
    border-radius: 2px;

    &.label-default {
      color: #AAA;
      font-weight: 500;
      background-color: #fff;
      border: 1px solid #eee;
      padding: .5em .7em .6em;
    }

    [data-role="remove"] {
      margin-left:8px;
      cursor:pointer;
      &:after{
        content: "x";
        padding:0px 2px;
      }
      &:hover {
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
        &:active {
          box-shadow: inset 0 3px 5px rgba(0,0,0,0.125);
        }
      }
    }
  }
}
