<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">

	<title>DataTables example - Form inputs</title>
	<link rel="stylesheet" type="text/css" href="../../media/css/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../resources/demo.css">
	<style type="text/css" class="init">

	</style>
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../media/js/jquery.dataTables.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../resources/demo.js"></script>
	<script type="text/javascript" language="javascript" class="init">


$(document).ready(function() {
	var table = $('#example').DataTable();

	$('button').click( function() {
		var data = table.$('input, select').serialize();
		alert(
			"The following data would have been submitted to the server: \n\n"+
			data.substr( 0, 120 )+'...'
		);
		return false;
	} );
} );


	</script>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>DataTables example <span>Form inputs</span></h1>

			<div class="info">
				<p>In order to perform paging, ordering, searching etc, DataTables can remove rows and cells from the
				document (i.e. those rows / cells which are not needed are not inserted into the document). This
				increases performance and compatibility, however, it means that submitting forms which span multiple
				pages requires a little bit of additional work to get the information that is not in the document any
				longer.</p>

				<p>The <a href="//datatables.net/reference/api/%24()"><code class="api" title=
				"DataTables API method">$()<span>DT</span></code></a> method can be used to get nodes from the document
				regardless of paging, ordering etc. This example shows <a href=
				"//datatables.net/reference/api/%24()"><code class="api" title=
				"DataTables API method">$()<span>DT</span></code></a> being used to get all <code class="tag" title=
				"HTML tag">input</code> elements from the table.</p>

				<p>In the example a simple <code>alert()</code> is used to show the information from the form, but an
				Ajax call to the server with the form data could easily be performed.</p>
			</div><button type="submit">Submit form</button>

			<table id="example" class="display" cellspacing="0" width="100%">
				<thead>
					<tr>
						<th>Name</th>
						<th>Age</th>
						<th>Position</th>
						<th>Office</th>
					</tr>
				</thead>

				<tfoot>
					<tr>
						<th>Name</th>
						<th>Age</th>
						<th>Position</th>
						<th>Office</th>
					</tr>
				</tfoot>

				<tbody>
					<tr>
						<td>Tiger Nixon</td>
						<td><input type="text" id="row-1-age" name="row-1-age" value="61"></td>
						<td><input type="text" id="row-1-position" name="row-1-position" value="System Architect"></td>
						<td><select size="1" id="row-1-office" name="row-1-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Garrett Winters</td>
						<td><input type="text" id="row-2-age" name="row-2-age" value="63"></td>
						<td><input type="text" id="row-2-position" name="row-2-position" value="Accountant"></td>
						<td><select size="1" id="row-2-office" name="row-2-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo" selected="selected">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Ashton Cox</td>
						<td><input type="text" id="row-3-age" name="row-3-age" value="66"></td>
						<td><input type="text" id="row-3-position" name="row-3-position" value=
						"Junior Technical Author"></td>
						<td><select size="1" id="row-3-office" name="row-3-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Cedric Kelly</td>
						<td><input type="text" id="row-4-age" name="row-4-age" value="22"></td>
						<td><input type="text" id="row-4-position" name="row-4-position" value=
						"Senior Javascript Developer"></td>
						<td><select size="1" id="row-4-office" name="row-4-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Airi Satou</td>
						<td><input type="text" id="row-5-age" name="row-5-age" value="33"></td>
						<td><input type="text" id="row-5-position" name="row-5-position" value="Accountant"></td>
						<td><select size="1" id="row-5-office" name="row-5-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo" selected="selected">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Brielle Williamson</td>
						<td><input type="text" id="row-6-age" name="row-6-age" value="61"></td>
						<td><input type="text" id="row-6-position" name="row-6-position" value=
						"Integration Specialist"></td>
						<td><select size="1" id="row-6-office" name="row-6-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Herrod Chandler</td>
						<td><input type="text" id="row-7-age" name="row-7-age" value="59"></td>
						<td><input type="text" id="row-7-position" name="row-7-position" value="Sales Assistant"></td>
						<td><select size="1" id="row-7-office" name="row-7-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Rhona Davidson</td>
						<td><input type="text" id="row-8-age" name="row-8-age" value="55"></td>
						<td><input type="text" id="row-8-position" name="row-8-position" value=
						"Integration Specialist"></td>
						<td><select size="1" id="row-8-office" name="row-8-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo" selected="selected">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Colleen Hurst</td>
						<td><input type="text" id="row-9-age" name="row-9-age" value="39"></td>
						<td><input type="text" id="row-9-position" name="row-9-position" value=
						"Javascript Developer"></td>
						<td><select size="1" id="row-9-office" name="row-9-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Sonya Frost</td>
						<td><input type="text" id="row-10-age" name="row-10-age" value="23"></td>
						<td><input type="text" id="row-10-position" name="row-10-position" value=
						"Software Engineer"></td>
						<td><select size="1" id="row-10-office" name="row-10-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Jena Gaines</td>
						<td><input type="text" id="row-11-age" name="row-11-age" value="30"></td>
						<td><input type="text" id="row-11-position" name="row-11-position" value="Office Manager"></td>
						<td><select size="1" id="row-11-office" name="row-11-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Quinn Flynn</td>
						<td><input type="text" id="row-12-age" name="row-12-age" value="22"></td>
						<td><input type="text" id="row-12-position" name="row-12-position" value="Support Lead"></td>
						<td><select size="1" id="row-12-office" name="row-12-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Charde Marshall</td>
						<td><input type="text" id="row-13-age" name="row-13-age" value="36"></td>
						<td><input type="text" id="row-13-position" name="row-13-position" value=
						"Regional Director"></td>
						<td><select size="1" id="row-13-office" name="row-13-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Haley Kennedy</td>
						<td><input type="text" id="row-14-age" name="row-14-age" value="43"></td>
						<td><input type="text" id="row-14-position" name="row-14-position" value=
						"Senior Marketing Designer"></td>
						<td><select size="1" id="row-14-office" name="row-14-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Tatyana Fitzpatrick</td>
						<td><input type="text" id="row-15-age" name="row-15-age" value="19"></td>
						<td><input type="text" id="row-15-position" name="row-15-position" value=
						"Regional Director"></td>
						<td><select size="1" id="row-15-office" name="row-15-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Michael Silva</td>
						<td><input type="text" id="row-16-age" name="row-16-age" value="66"></td>
						<td><input type="text" id="row-16-position" name="row-16-position" value=
						"Marketing Designer"></td>
						<td><select size="1" id="row-16-office" name="row-16-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Paul Byrd</td>
						<td><input type="text" id="row-17-age" name="row-17-age" value="64"></td>
						<td><input type="text" id="row-17-position" name="row-17-position" value=
						"Chief Financial Officer (CFO)"></td>
						<td><select size="1" id="row-17-office" name="row-17-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Gloria Little</td>
						<td><input type="text" id="row-18-age" name="row-18-age" value="59"></td>
						<td><input type="text" id="row-18-position" name="row-18-position" value=
						"Systems Administrator"></td>
						<td><select size="1" id="row-18-office" name="row-18-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Bradley Greer</td>
						<td><input type="text" id="row-19-age" name="row-19-age" value="41"></td>
						<td><input type="text" id="row-19-position" name="row-19-position" value=
						"Software Engineer"></td>
						<td><select size="1" id="row-19-office" name="row-19-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Dai Rios</td>
						<td><input type="text" id="row-20-age" name="row-20-age" value="35"></td>
						<td><input type="text" id="row-20-position" name="row-20-position" value="Personnel Lead"></td>
						<td><select size="1" id="row-20-office" name="row-20-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Jenette Caldwell</td>
						<td><input type="text" id="row-21-age" name="row-21-age" value="30"></td>
						<td><input type="text" id="row-21-position" name="row-21-position" value=
						"Development Lead"></td>
						<td><select size="1" id="row-21-office" name="row-21-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Yuri Berry</td>
						<td><input type="text" id="row-22-age" name="row-22-age" value="40"></td>
						<td><input type="text" id="row-22-position" name="row-22-position" value=
						"Chief Marketing Officer (CMO)"></td>
						<td><select size="1" id="row-22-office" name="row-22-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Caesar Vance</td>
						<td><input type="text" id="row-23-age" name="row-23-age" value="21"></td>
						<td><input type="text" id="row-23-position" name="row-23-position" value=
						"Pre-Sales Support"></td>
						<td><select size="1" id="row-23-office" name="row-23-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Doris Wilder</td>
						<td><input type="text" id="row-24-age" name="row-24-age" value="23"></td>
						<td><input type="text" id="row-24-position" name="row-24-position" value=
						"Sales Assistant"></td>
						<td><select size="1" id="row-24-office" name="row-24-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Angelica Ramos</td>
						<td><input type="text" id="row-25-age" name="row-25-age" value="47"></td>
						<td><input type="text" id="row-25-position" name="row-25-position" value=
						"Chief Executive Officer (CEO)"></td>
						<td><select size="1" id="row-25-office" name="row-25-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Gavin Joyce</td>
						<td><input type="text" id="row-26-age" name="row-26-age" value="42"></td>
						<td><input type="text" id="row-26-position" name="row-26-position" value="Developer"></td>
						<td><select size="1" id="row-26-office" name="row-26-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Jennifer Chang</td>
						<td><input type="text" id="row-27-age" name="row-27-age" value="28"></td>
						<td><input type="text" id="row-27-position" name="row-27-position" value=
						"Regional Director"></td>
						<td><select size="1" id="row-27-office" name="row-27-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Brenden Wagner</td>
						<td><input type="text" id="row-28-age" name="row-28-age" value="28"></td>
						<td><input type="text" id="row-28-position" name="row-28-position" value=
						"Software Engineer"></td>
						<td><select size="1" id="row-28-office" name="row-28-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Fiona Green</td>
						<td><input type="text" id="row-29-age" name="row-29-age" value="48"></td>
						<td><input type="text" id="row-29-position" name="row-29-position" value=
						"Chief Operating Officer (COO)"></td>
						<td><select size="1" id="row-29-office" name="row-29-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Shou Itou</td>
						<td><input type="text" id="row-30-age" name="row-30-age" value="20"></td>
						<td><input type="text" id="row-30-position" name="row-30-position" value=
						"Regional Marketing"></td>
						<td><select size="1" id="row-30-office" name="row-30-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo" selected="selected">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Michelle House</td>
						<td><input type="text" id="row-31-age" name="row-31-age" value="37"></td>
						<td><input type="text" id="row-31-position" name="row-31-position" value=
						"Integration Specialist"></td>
						<td><select size="1" id="row-31-office" name="row-31-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Suki Burks</td>
						<td><input type="text" id="row-32-age" name="row-32-age" value="53"></td>
						<td><input type="text" id="row-32-position" name="row-32-position" value="Developer"></td>
						<td><select size="1" id="row-32-office" name="row-32-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Prescott Bartlett</td>
						<td><input type="text" id="row-33-age" name="row-33-age" value="27"></td>
						<td><input type="text" id="row-33-position" name="row-33-position" value=
						"Technical Author"></td>
						<td><select size="1" id="row-33-office" name="row-33-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Gavin Cortez</td>
						<td><input type="text" id="row-34-age" name="row-34-age" value="22"></td>
						<td><input type="text" id="row-34-position" name="row-34-position" value="Team Leader"></td>
						<td><select size="1" id="row-34-office" name="row-34-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Martena Mccray</td>
						<td><input type="text" id="row-35-age" name="row-35-age" value="46"></td>
						<td><input type="text" id="row-35-position" name="row-35-position" value=
						"Post-Sales support"></td>
						<td><select size="1" id="row-35-office" name="row-35-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Unity Butler</td>
						<td><input type="text" id="row-36-age" name="row-36-age" value="47"></td>
						<td><input type="text" id="row-36-position" name="row-36-position" value=
						"Marketing Designer"></td>
						<td><select size="1" id="row-36-office" name="row-36-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Howard Hatfield</td>
						<td><input type="text" id="row-37-age" name="row-37-age" value="51"></td>
						<td><input type="text" id="row-37-position" name="row-37-position" value="Office Manager"></td>
						<td><select size="1" id="row-37-office" name="row-37-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Hope Fuentes</td>
						<td><input type="text" id="row-38-age" name="row-38-age" value="41"></td>
						<td><input type="text" id="row-38-position" name="row-38-position" value="Secretary"></td>
						<td><select size="1" id="row-38-office" name="row-38-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Vivian Harrell</td>
						<td><input type="text" id="row-39-age" name="row-39-age" value="62"></td>
						<td><input type="text" id="row-39-position" name="row-39-position" value=
						"Financial Controller"></td>
						<td><select size="1" id="row-39-office" name="row-39-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Timothy Mooney</td>
						<td><input type="text" id="row-40-age" name="row-40-age" value="37"></td>
						<td><input type="text" id="row-40-position" name="row-40-position" value="Office Manager"></td>
						<td><select size="1" id="row-40-office" name="row-40-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Jackson Bradshaw</td>
						<td><input type="text" id="row-41-age" name="row-41-age" value="65"></td>
						<td><input type="text" id="row-41-position" name="row-41-position" value="Director"></td>
						<td><select size="1" id="row-41-office" name="row-41-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Olivia Liang</td>
						<td><input type="text" id="row-42-age" name="row-42-age" value="64"></td>
						<td><input type="text" id="row-42-position" name="row-42-position" value=
						"Support Engineer"></td>
						<td><select size="1" id="row-42-office" name="row-42-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Bruno Nash</td>
						<td><input type="text" id="row-43-age" name="row-43-age" value="38"></td>
						<td><input type="text" id="row-43-position" name="row-43-position" value=
						"Software Engineer"></td>
						<td><select size="1" id="row-43-office" name="row-43-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Sakura Yamamoto</td>
						<td><input type="text" id="row-44-age" name="row-44-age" value="37"></td>
						<td><input type="text" id="row-44-position" name="row-44-position" value=
						"Support Engineer"></td>
						<td><select size="1" id="row-44-office" name="row-44-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo" selected="selected">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Thor Walton</td>
						<td><input type="text" id="row-45-age" name="row-45-age" value="61"></td>
						<td><input type="text" id="row-45-position" name="row-45-position" value="Developer"></td>
						<td><select size="1" id="row-45-office" name="row-45-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Finn Camacho</td>
						<td><input type="text" id="row-46-age" name="row-46-age" value="47"></td>
						<td><input type="text" id="row-46-position" name="row-46-position" value=
						"Support Engineer"></td>
						<td><select size="1" id="row-46-office" name="row-46-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Serge Baldwin</td>
						<td><input type="text" id="row-47-age" name="row-47-age" value="64"></td>
						<td><input type="text" id="row-47-position" name="row-47-position" value=
						"Data Coordinator"></td>
						<td><select size="1" id="row-47-office" name="row-47-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Zenaida Frank</td>
						<td><input type="text" id="row-48-age" name="row-48-age" value="63"></td>
						<td><input type="text" id="row-48-position" name="row-48-position" value=
						"Software Engineer"></td>
						<td><select size="1" id="row-48-office" name="row-48-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Zorita Serrano</td>
						<td><input type="text" id="row-49-age" name="row-49-age" value="56"></td>
						<td><input type="text" id="row-49-position" name="row-49-position" value=
						"Software Engineer"></td>
						<td><select size="1" id="row-49-office" name="row-49-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Jennifer Acosta</td>
						<td><input type="text" id="row-50-age" name="row-50-age" value="43"></td>
						<td><input type="text" id="row-50-position" name="row-50-position" value=
						"Junior Javascript Developer"></td>
						<td><select size="1" id="row-50-office" name="row-50-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Cara Stevens</td>
						<td><input type="text" id="row-51-age" name="row-51-age" value="46"></td>
						<td><input type="text" id="row-51-position" name="row-51-position" value=
						"Sales Assistant"></td>
						<td><select size="1" id="row-51-office" name="row-51-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Hermione Butler</td>
						<td><input type="text" id="row-52-age" name="row-52-age" value="47"></td>
						<td><input type="text" id="row-52-position" name="row-52-position" value=
						"Regional Director"></td>
						<td><select size="1" id="row-52-office" name="row-52-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Lael Greer</td>
						<td><input type="text" id="row-53-age" name="row-53-age" value="21"></td>
						<td><input type="text" id="row-53-position" name="row-53-position" value=
						"Systems Administrator"></td>
						<td><select size="1" id="row-53-office" name="row-53-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London" selected="selected">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Jonas Alexander</td>
						<td><input type="text" id="row-54-age" name="row-54-age" value="30"></td>
						<td><input type="text" id="row-54-position" name="row-54-position" value="Developer"></td>
						<td><select size="1" id="row-54-office" name="row-54-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco" selected="selected">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Shad Decker</td>
						<td><input type="text" id="row-55-age" name="row-55-age" value="51"></td>
						<td><input type="text" id="row-55-position" name="row-55-position" value=
						"Regional Director"></td>
						<td><select size="1" id="row-55-office" name="row-55-office">
							<option value="Edinburgh" selected="selected">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Michael Bruce</td>
						<td><input type="text" id="row-56-age" name="row-56-age" value="29"></td>
						<td><input type="text" id="row-56-position" name="row-56-position" value=
						"Javascript Developer"></td>
						<td><select size="1" id="row-56-office" name="row-56-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
					<tr>
						<td>Donna Snider</td>
						<td><input type="text" id="row-57-age" name="row-57-age" value="27"></td>
						<td><input type="text" id="row-57-position" name="row-57-position" value=
						"Customer Support"></td>
						<td><select size="1" id="row-57-office" name="row-57-office">
							<option value="Edinburgh">
								Edinburgh
							</option>

							<option value="London">
								London
							</option>

							<option value="New York" selected="selected">
								New York
							</option>

							<option value="San Francisco">
								San Francisco
							</option>

							<option value="Tokyo">
								Tokyo
							</option>
						</select></td>
					</tr>
				</tbody>
			</table>

			<ul class="tabs">
				<li class="active">Javascript</li>
				<li>HTML</li>
				<li>CSS</li>
				<li>Ajax</li>
				<li>Server-side script</li>
			</ul>

			<div class="tabs">
				<div class="js">
					<p>The Javascript shown below is used to initialise the table shown in this
					example:</p><code class="multiline brush: js;">$(document).ready(function() {
	var table = $('#example').DataTable();

	$('button').click( function() {
		var data = table.$('input, select').serialize();
		alert(
			&quot;The following data would have been submitted to the server: \n\n&quot;+
			data.substr( 0, 120 )+'...'
		);
		return false;
	} );
} );</code>

					<p>In addition to the above code, the following Javascript library files are loaded for use in this
					example:</p>

					<ul>
						<li><a href="../../media/js/jquery.js">../../media/js/jquery.js</a></li>
						<li><a href="../../media/js/jquery.dataTables.js">../../media/js/jquery.dataTables.js</a></li>
					</ul>
				</div>

				<div class="table">
					<p>The HTML shown below is the raw HTML table element, before it has been enhanced by
					DataTables:</p>
				</div>

				<div class="css">
					<div>
						<p>This example uses a little bit of additional CSS beyond what is loaded from the library
						files (below), in order to correctly display the table. The additional CSS used is shown
						below:</p><code class="multiline brush: js;"></code>
					</div>

					<p>The following CSS library files are loaded for use in this example to provide the styling of the
					table:</p>

					<ul>
						<li><a href=
						"../../media/css/jquery.dataTables.css">../../media/css/jquery.dataTables.css</a></li>
					</ul>
				</div>

				<div class="ajax">
					<p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data
					will update automatically as any additional data is loaded.</p>
				</div>

				<div class="php">
					<p>The script used to perform the server-side processing for this table is shown below. Please note
					that this is just an example script using PHP. Server-side processing scripts can be written in any
					language, using <a href="//datatables.net/manual/server-side">the protocol described in the
					DataTables documentation</a>.</p>
				</div>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<h2>Other examples</h2>

				<div class="toc">
					<div class="toc-group">
						<h3><a href="../basic_init/index.html">Basic initialisation</a></h3>
						<ul class="toc">
							<li><a href="../basic_init/zero_configuration.html">Zero configuration</a></li>
							<li><a href="../basic_init/filter_only.html">Feature enable / disable</a></li>
							<li><a href="../basic_init/table_sorting.html">Default ordering (sorting)</a></li>
							<li><a href="../basic_init/multi_col_sort.html">Multi-column ordering</a></li>
							<li><a href="../basic_init/multiple_tables.html">Multiple tables</a></li>
							<li><a href="../basic_init/hidden_columns.html">Hidden columns</a></li>
							<li><a href="../basic_init/complex_header.html">Complex headers (rowspan and
							colspan)</a></li>
							<li><a href="../basic_init/dom.html">DOM positioning</a></li>
							<li><a href="../basic_init/flexible_width.html">Flexible table width</a></li>
							<li><a href="../basic_init/state_save.html">State saving</a></li>
							<li><a href="../basic_init/alt_pagination.html">Alternative pagination</a></li>
							<li><a href="../basic_init/scroll_y.html">Scroll - vertical</a></li>
							<li><a href="../basic_init/scroll_x.html">Scroll - horizontal</a></li>
							<li><a href="../basic_init/scroll_xy.html">Scroll - horizontal and vertical</a></li>
							<li><a href="../basic_init/scroll_y_theme.html">Scroll - vertical with jQuery UI
							ThemeRoller</a></li>
							<li><a href="../basic_init/comma-decimal.html">Language - Comma decimal place</a></li>
							<li><a href="../basic_init/language.html">Language options</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../advanced_init/index.html">Advanced initialisation</a></h3>
						<ul class="toc">
							<li><a href="../advanced_init/events_live.html">DOM / jQuery events</a></li>
							<li><a href="../advanced_init/dt_events.html">DataTables events</a></li>
							<li><a href="../advanced_init/column_render.html">Column rendering</a></li>
							<li><a href="../advanced_init/length_menu.html">Page length options</a></li>
							<li><a href="../advanced_init/dom_multiple_elements.html">Multiple table control
							elements</a></li>
							<li><a href="../advanced_init/complex_header.html">Complex headers (rowspan /
							colspan)</a></li>
							<li><a href="../advanced_init/object_dom_read.html">Read HTML to data objects</a></li>
							<li><a href="../advanced_init/html5-data-attributes.html">HTML5 data-* attributes</a></li>
							<li><a href="../advanced_init/language_file.html">Language file</a></li>
							<li><a href="../advanced_init/defaults.html">Setting defaults</a></li>
							<li><a href="../advanced_init/row_callback.html">Row created callback</a></li>
							<li><a href="../advanced_init/row_grouping.html">Row grouping</a></li>
							<li><a href="../advanced_init/footer_callback.html">Footer callback</a></li>
							<li><a href="../advanced_init/dom_toolbar.html">Custom toolbar elements</a></li>
							<li><a href="../advanced_init/sort_direction_control.html">Order direction sequence
							control</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="../styling/display.html">Base style</a></li>
							<li><a href="../styling/no-classes.html">Base style - no styling classes</a></li>
							<li><a href="../styling/cell-border.html">Base style - cell borders</a></li>
							<li><a href="../styling/compact.html">Base style - compact</a></li>
							<li><a href="../styling/hover.html">Base style - hover</a></li>
							<li><a href="../styling/order-column.html">Base style - order-column</a></li>
							<li><a href="../styling/row-border.html">Base style - row borders</a></li>
							<li><a href="../styling/stripe.html">Base style - stripe</a></li>
							<li><a href="../styling/bootstrap.html">Bootstrap</a></li>
							<li><a href="../styling/foundation.html">Foundation</a></li>
							<li><a href="../styling/jqueryUI.html">jQuery UI ThemeRoller</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../data_sources/index.html">Data sources</a></h3>
						<ul class="toc">
							<li><a href="../data_sources/dom.html">HTML (DOM) sourced data</a></li>
							<li><a href="../data_sources/ajax.html">Ajax sourced data</a></li>
							<li><a href="../data_sources/js_array.html">Javascript sourced data</a></li>
							<li><a href="../data_sources/server_side.html">Server-side processing</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./index.html">API</a></h3>
						<ul class="toc active">
							<li><a href="./add_row.html">Add rows</a></li>
							<li><a href="./multi_filter.html">Individual column searching (text inputs)</a></li>
							<li><a href="./multi_filter_select.html">Individual column searching (select
							inputs)</a></li>
							<li><a href="./highlight.html">Highlighting rows and columns</a></li>
							<li><a href="./row_details.html">Child rows (show extra / detailed information)</a></li>
							<li><a href="./select_row.html">Row selection (multiple rows)</a></li>
							<li><a href="./select_single_row.html">Row selection and deletion (single row)</a></li>
							<li class="active"><a href="./form.html">Form inputs</a></li>
							<li><a href="./counter_columns.html">Index column</a></li>
							<li><a href="./show_hide.html">Show / hide columns dynamically</a></li>
							<li><a href="./api_in_init.html">Using API in callbacks</a></li>
							<li><a href="./tabs_and_scrolling.html">Scrolling and jQuery UI tabs</a></li>
							<li><a href="./regex.html">Search API (regular expressions)</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../ajax/index.html">Ajax</a></h3>
						<ul class="toc">
							<li><a href="../ajax/simple.html">Ajax data source (arrays)</a></li>
							<li><a href="../ajax/objects.html">Ajax data source (objects)</a></li>
							<li><a href="../ajax/deep.html">Nested object data (objects)</a></li>
							<li><a href="../ajax/objects_subarrays.html">Nested object data (arrays)</a></li>
							<li><a href="../ajax/orthogonal-data.html">Orthogonal data</a></li>
							<li><a href="../ajax/null_data_source.html">Generated content for a column</a></li>
							<li><a href="../ajax/custom_data_property.html">Custom data source property</a></li>
							<li><a href="../ajax/custom_data_flat.html">Flat array data source</a></li>
							<li><a href="../ajax/defer_render.html">Deferred rendering for speed</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../server_side/index.html">Server-side</a></h3>
						<ul class="toc">
							<li><a href="../server_side/simple.html">Server-side processing</a></li>
							<li><a href="../server_side/custom_vars.html">Custom HTTP variables</a></li>
							<li><a href="../server_side/post.html">POST data</a></li>
							<li><a href="../server_side/ids.html">Automatic addition of row ID attributes</a></li>
							<li><a href="../server_side/object_data.html">Object data source</a></li>
							<li><a href="../server_side/row_details.html">Row details</a></li>
							<li><a href="../server_side/select_rows.html">Row selection</a></li>
							<li><a href="../server_side/jsonp.html">JSONP data source for remote domains</a></li>
							<li><a href="../server_side/defer_loading.html">Deferred loading of data</a></li>
							<li><a href="../server_side/pipeline.html">Pipelining data to reduce Ajax calls for
							paging</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../plug-ins/index.html">Plug-ins</a></h3>
						<ul class="toc">
							<li><a href="../plug-ins/api.html">API plug-in methods</a></li>
							<li><a href="../plug-ins/sorting_auto.html">Ordering plug-ins (with type
							detection)</a></li>
							<li><a href="../plug-ins/sorting_manual.html">Ordering plug-ins (no type
							detection)</a></li>
							<li><a href="../plug-ins/range_filtering.html">Custom filtering - range search</a></li>
							<li><a href="../plug-ins/dom_sort.html">Live DOM ordering</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>