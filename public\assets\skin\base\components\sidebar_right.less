/* ==============================================
   Right Sidebar
     A. Default Closed State
     B. Sidebar Right Panel Menu
     C. Nano Sidebar Scroller Settings
=================================================
  A. Default Closed State
================================================= */
#sidebar_right {
    position: fixed;
    width: 300px;
    height: 100%;
    top: 60px;
    right: -300px;
    border-left: 1px solid #ddd;
    background: #f8f8f8;
}
#sidebar_right .sidebar-right-header {
    width: 100%;
    height: 59px;
    padding: 4px 10px 4px 20px;
}
#sidebar_right .sidebar_right_content a:hover {
    text-decoration: none;
}

/*===============================================
   B. Sidebar Right Panel Menu
================================================= */
.title-divider {
    border-bottom: 1px solid #e8e8e8;
    padding: 0 5px 9px 5px;
}
#sidebar_right .panel {
    -webkit-box-shadow: none;
    box-shadow: none;
}
#sidebar_right .panel-heading {
    height: 51px;
    min-height: 51px;
    overflow: hidden;
}
#sidebar_right .panel-tabs li a {
    padding: 17px 18px;
    border-right: 1px solid transparent;
}
#sidebar_right .panel-tabs li:first-child a {
    border-left: 1px solid transparent;
}

/*===============================================
  C. Nano Sidebar Scroller Settings
================================================= */
#sidebar_right.nano > .nano-pane {
    background: rgba(0, 0, 0, .07);
}
#sidebar_right.nano > .nano-pane > .nano-slider {
    background: #444;
    background: #CCC;
}
