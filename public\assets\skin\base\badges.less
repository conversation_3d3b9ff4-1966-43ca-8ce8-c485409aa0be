/*==================================================
  Badges
==================================================== */

// Base class
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: @badge-font-size;
  font-weight: @badge-font-weight;
  color: @badge-color;
  line-height: @badge-line-height;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: @badge-bg;
  border-radius: @badge-border-radius;

  // Empty badges collapse automatically (not available in IE8)
  &:empty {
    display: none;
  }

  // Quick fix for badges in buttons
  .btn & {
    position: relative;
    top: -1px;
  }
  .btn-xs & {
    top: 0;
    padding: 1px 5px;
  }

  // Hover state, but only for links
  a& {
    &:hover,
    &:focus {
      color: @badge-link-hover-color;
      text-decoration: none;
      cursor: pointer;
    }
  }

  // Account for badges in navs
  a.list-group-item.active > &,
  .nav-pills > .active > a > & {
    color: @badge-active-color;
    background-color: @badge-active-bg;
  }
  .nav-pills > li > a > & {
    margin-left: 3px;
  }
}


// Contextual Skin Variations (linked labels get darker on :hover)
.badge-default {
  .label-variant(@label-default-bg);
}
.badge-primary when (@skin-primary) {
  .label-variant(@label-primary-bg);
}
.badge-success when (@skin-success) {
  .label-variant(@label-success-bg);
}
.badge-info when (@skin-info) {
  .label-variant(@label-info-bg);
}
.badge-warning when (@skin-warning) {
  .label-variant(@label-warning-bg);
}
.badge-danger when (@skin-danger) {
  .label-variant(@label-danger-bg);
}
.badge-alert when (@skin-alert) {
  .label-variant(@label-alert-bg);
}
.badge-system when (@skin-system) {
  .label-variant(@label-system-bg);
}
.badge-dark when (@skin-dark) {
  .label-variant(@label-dark-bg);
}
